package com.novel.disk.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.novel.disk.common.ApiUtils;
import com.novel.disk.common.BaseController;
import com.novel.disk.common.FileUtils;
import com.novel.disk.common.Result;
import com.novel.disk.common.annotation.Log;
import com.novel.disk.common.enums.BusinessType;
import com.novel.disk.common.obj.AjaxResult;
import com.novel.disk.common.obj.BaseEntity;
import com.novel.disk.common.utils.oss.OssUtils;
import com.novel.disk.entity.FileEntity;
import com.novel.disk.framework.config.ChjdConfig;
import com.novel.disk.service.FileService;

import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.thread.ThreadUtil;
import lombok.SneakyThrows;

/**
 * 文件
 *
 * <AUTHOR>
 * @date 2020/7/13
 */
@RestController
@RequestMapping("/file")
public class FileController extends BaseController {
	private final FileService fileService;

	public FileController(FileService fileService) {
		this.fileService = fileService;
	}

	/**
	 * 新建目录
	 *
	 * @param dirName 目录名称
	 * @param pid     当前目录的id
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.MKDIR)
	@GetMapping("/mkdir")
	public Result mkdir(@RequestParam("dirName") String dirName, @RequestParam("pid") Long pid) {
		return toAjax(fileService.mkdir(dirName, pid, getUserId()));
	}

	/**
	 * 列出某个目录下所有文件和文件夹
	 *
	 * @param fileEntity 查询条件
	 * @return 结果
	 */
	@GetMapping("/listDir")
	public Result listDir(FileEntity fileEntity) {
		fileEntity.setUserId(getUserId());
		return toAjax(fileService.listDir(fileEntity));
	}

	@GetMapping("/listDirTree")
	public Result listDirTree() {
		return toAjax(fileService.listDirTree(getUserId()));
	}

	/**
	 * 列出文件树
	 * @return
	 */
	@GetMapping("/listFileTree")
	public Result listFileTree(FileEntity fileEntity) {
		fileEntity.setUserId(getUserId());
		return toAjax(fileService.listFileTree(fileEntity));
	}

	/**
	 * 根据id获取目录
	 * @param file
	 * @param pid
	 * @return
	 */
	@GetMapping("/getFileById")
	public Result getFileById(Long pid) {
		return toAjax(fileService.getFileById(pid));
	}

	/**
	 * 上传文件
	 *
	 * @param file 上传的文件
	 * @param pid  文件目录
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.UPLOAD)
	@PostMapping("/upLoad")
	public AjaxResult upLoad(MultipartFile file, @RequestParam(value = "pid", required = false) Long pid) {
		return toAjaxRes(fileService.upLoad(file, pid, getUserId()));
	}

	/**
	 * 批量上传文件
	 */
	@Log(title = "企业网盘", businessType = BusinessType.UPLOAD)
	@PostMapping("/upLoads")
	public AjaxResult upLoads(@RequestParam("files") MultipartFile[] files, @RequestParam(value = "pid", required = false) Long pid) {
		return AjaxResult.success(fileService.upLoads(files, pid, getUserId()));
	}

	/**
	 * 删除指定资源
	 *
	 * @param sourceListId 资源id集合
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.DELETE_FILE)
	@DeleteMapping("/remove/{sourceListId}")
	public Result remove(@PathVariable Long[] sourceListId) {
		return toAjax(fileService.remove(sourceListId, getUserId()));
	}

	/**
	 * 重命名
	 *
	 * @param id   资源id
	 * @param name 资源名称
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.RENAME)
	@GetMapping("/rename")
	public Result rename(@RequestParam("id") Long id, @RequestParam("name") String name) {
		return toAjax(fileService.rename(id, name, getUserId()));
	}

	/**
	 * 资源复制
	 *
	 * @param sourceListId 资源id集合
	 * @param targetId     目标目录
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.COPY_DIR)
	@PostMapping("copyDir")
	public Result copyDir(@RequestBody BaseEntity baseEntity) {
		Map<String, Object> map = baseEntity.getParams();
		// 提取sourceListId
		Long[] sourceListId = getSourceIdListFromMap(map);
		// 提取targetId
		Long targetId = Long.valueOf(map.get("targetId").toString());
		return toAjax(fileService.copyDir(sourceListId, targetId, getUserId()));
	}

	/**
	 * 资源移动
	 *
	 * @param sourceListId 资源id集合
	 * @param targetId     目标目录
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.MOVE_DIR)
	@PostMapping("/moveDir")
	public Result moveDir(@RequestBody BaseEntity baseEntity) {
		Map<String, Object> map = baseEntity.getParams();
		// 提取sourceListId
		Long[] sourceListId = getSourceIdListFromMap(map);
		// 提取targetId
		Long targetId = Long.valueOf(map.get("targetId").toString());
		return toAjax(fileService.moveDir(sourceListId, targetId, getUserId()));
	}

	// 提取sourceListId
	private Long[] getSourceIdListFromMap(Map<String, Object> map) {
		String sourceIds = map.get("sourceListId").toString();
		// 去掉两边的[]并转为字符数组
		String[] ids = sourceIds.substring(1, sourceIds.length() -1).split(",");
		Long[] sourceListId = new Long[ids.length];
		// 将字符数组转为Long数组
		for (int i = 0; i < ids.length; i++) {
			sourceListId[i] = Long.parseLong(ids[i]);
		}
		return sourceListId;
	}

	@GetMapping("/getDownloadUrl")
	public Result getDownloadUrl(@RequestParam("fileId") Long fileId) {
		FileEntity fileEntity = fileService.getDownloadUrl(fileId);
		if (fileEntity != null) {
			Map<Object, Object> map = new HashMap<>(4);
			map.put("url", "/file/download?url=" + ApiUtils.encodeBase64(fileEntity.getUrl()) + "&exp="
					+ (System.currentTimeMillis() + 300000) + "&fileName=" + fileEntity.getName());
			map.put("fileId", fileEntity.getFileId());
			map.put("fileName", fileEntity.getName());
			return toAjax(map);
		}
		return error();
	}
	
	/**
	 *  网盘文件下载【对于单个文件，直接下载文件，对于文件夹，下载压缩包，对于混合，下载压缩包】
	 */
	// @Log(title = "企业网盘", businessType = BusinessType.DOWNLOAD)
	@SneakyThrows
	@GetMapping("/fileDownload")
	public void fileDownload(HttpServletResponse resp, @RequestParam("fileId") Long fileId, @RequestParam("userId") Long userId) {
		fileService.fileDownload(resp, fileId, userId);
	}

	/**
	 * 通用下载请求
	 *
	 * @param url 文件路径
	 * @param exp 超时时间
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.DOWNLOAD)
	@SneakyThrows
	@GetMapping("/download")
	public ResponseEntity<byte[]> fileDownload(@RequestParam("url") String url,
			@RequestHeader("user-agent") String userAgent,
			@RequestParam(name = "fileName", required = false) String fileName,
			@RequestParam(name = "exp", required = false) Long exp,
			@RequestParam(name = "isDelete", required = false) boolean isDelete) {
		File file = fileService.readBytes(ApiUtils.decodeBase64(url));
		byte[] body = FileUtils.getBytesByFile(file);
    //    byte[] body = OssUtils.downloadZipFromOss(filePaths);

		if (!fileName.contains(".")) {
			fileName += ".zip";
			if (isDelete) {
				file.delete();
			}
		}
		HttpHeaders headers = new HttpHeaders();
		headers.setContentLength(body.length);

		headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		ContentDisposition disposition = ContentDisposition.builder("attachment")
				.filename(FileUtils.setFileDownloadHeader(userAgent, fileName)).build();
		headers.setContentDisposition(disposition);
		return new ResponseEntity<>(body, headers, HttpStatus.OK);
	}

		/**
	 * 通用下载请求
	 *
	 * @param url 文件路径
	 * @param exp 超时时间
	 * @return 结果
	 */
	@Log(title = "企业网盘", businessType = BusinessType.DOWNLOAD)
	@SneakyThrows
	@GetMapping("/_download")
	public void _fileDownload( HttpServletResponse resp, @RequestParam("filePath") String filePath,
			@RequestHeader("user-agent") String userAgent,
			@RequestParam(name = "fileName", required = false) String fileName,
			@RequestParam(name = "exp", required = false) Long exp,
			@RequestParam(name = "isDelete", required = false) boolean isDelete) {
		// File file = fileService.readBytes(ApiUtils.decodeBase64(url));
		// byte[] body = FileUtils.getBytesByFile(file);
		
		System.out.println("filePath:"+filePath);
		System.out.println("fileName:"+fileName);
		// if (!fileName.contains(".")) {
			// 	fileName += ".zip";
			// 	List<String> filePaths = new ArrayList<>();
			// 	// 根据filePath获取filePaths
			// 	body = OssUtils.downloadZipFromOss(filePaths);
			// }
		// body = OssUtils.downloadFromOss(filePath);
			
		// Long contentLength = OssUtils.getOssFileLength(filePath);
		// System.out.println("length:"+contentLength);
		OssUtils.downloadFromOss(resp, filePath, fileName);
	}

	@PostMapping("/unzip/{fileId}")
	public Result unzip(@PathVariable Long fileId) {
		boolean res = fileService.unzip(fileId, getUserId());
		return toAjax(res);
	}

	@GetMapping("getTempDownloadUrl")
	public Result getTempDownloadUrl(@RequestParam("filePath") String filePath) {
		// 异步下载oss文件到临时目录
		OssUtils.downloadAndStoreToLocal(ChjdConfig.getPanFile() + filePath, ChjdConfig.getTmpFile());
		// ThreadUtil.execAsync(() -> OssUtils.downloadAndStoreToLocal(filePath, ChjdConfig.getTmpFile()));
		// 从filePath中截取文件名
		String fileName = filePath.substring(filePath.lastIndexOf("/")+1, filePath.length());
		// 根据文件名和临时目录组装下载地址
		String url = "https://aftersale.charmhigh.com/chjd/tmpFile/"+fileName;
		// 返回下载地址
		return Result.success(url);
	}
}
