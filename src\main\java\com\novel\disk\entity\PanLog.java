package com.novel.disk.entity;

import java.util.Date;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.novel.disk.common.enums.BusinessType;
import com.novel.disk.manager.AsyncManager;
import com.novel.disk.manager.factory.AsyncFactory;

import lombok.Data;

@Data
@Entity
@Table(name = "pan_log")
public class PanLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long logId;

    @Column(columnDefinition = "varchar(50) COMMENT '操作模块'")
    private String moduleName;

    @Column(columnDefinition = "int COMMENT '操作类型'")
    private Integer businessType;

    @Column(columnDefinition = "varchar(50) COMMENT '操作人员'")
    private String operName;

    @Column(columnDefinition = "varchar(255) COMMENT '操作文件'")
    private String fileName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(columnDefinition = "datetime COMMENT '操作时间'")
    private Date operTime;

    @Column(columnDefinition = "bit COMMENT '操作结果'")
    private Boolean status;

    @Column(columnDefinition = "varchar(255) COMMENT '附加信息'")
    private String addtional;

    /**
     * 组装日志信息
     */
    @Deprecated
    public static void makeLog(BusinessType businessType, String userName, String fileName, Boolean status, String addtionalMsg) {
        PanLog log = new PanLog();
        log.setBusinessType(businessType.ordinal());
        log.setOperName(userName);
        log.setFileName(fileName);
        log.setStatus(status);
        log.setAddtional(addtionalMsg);
        AsyncManager.me().execute(AsyncFactory.recordDiskOper(log));
    }

    /**
     * 组装日志信息
     */
    @Deprecated
    public static void makeLogs(BusinessType businessType, String userName, String[] fileNames, Boolean status, String addtionalMsg) {
        for (String fileName : fileNames) {
            makeLog(businessType, userName, fileName, status, addtionalMsg);
        }
    }


}