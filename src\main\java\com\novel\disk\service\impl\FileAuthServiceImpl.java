package com.novel.disk.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.persistence.EntityManager;

import com.novel.disk.entity.FileAuthEntity;
import com.novel.disk.entity.FileEntity;
import com.novel.disk.repository.FileAuthRepository;
import com.novel.disk.repository.FileRepository;
import com.novel.disk.service.FileAuthService;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class FileAuthServiceImpl implements FileAuthService {

    private final FileAuthRepository fileAuthRepository;

    private final FileRepository fileRepository;

    private EntityManager em;

    /**
     * 根据userId查询用户所拥有的权限
     */
    @Override
    public List<FileAuthEntity> getFileAuthByUserId(Long userId) {
        // 如果是管理员，则返回所有权限【所有权限指代的并非是file_auth表中的所有权限，而是查出所有的文件id,使用{fileId,userId,perm}】
        if (isRootUser(userId)) {
            List<FileAuthEntity> fileAuths = new ArrayList<>();
            List<FileEntity> files = fileRepository.findAll();
            for (FileEntity file : files) {
                FileAuthEntity fileAuth = new FileAuthEntity();
                fileAuth.setFileId(file.getFileId());
                fileAuth.setUserId(userId);
                fileAuth.setPerm(4L);
                fileAuths.add(fileAuth);
            }
            return fileAuths;
        }
        // 获取用户拥有权限的文件，先获取所有的文件，然后使用用户的文件的权限进行过滤
        // 先获取用户的所有权限
        List<FileAuthEntity> fileAuths = fileAuthRepository.getFileAuthByUserId(userId);
        // 然后获取authFileIdList
        List<Long> authFileIdList = new ArrayList<>();
        for (FileAuthEntity fileAuth : fileAuths) {
            authFileIdList.add(fileAuth.getFileId());
        }
        List<FileEntity> authFileList = fileRepository.findAllById(authFileIdList);
        // 获取孤立点
        List<FileEntity> orphanFileList = getOrphanFile(authFileList);
        // 通过孤立点递归出所有的文件权限
        List<FileAuthEntity> userAuthList = new ArrayList<>();
        getUserFileAuth(userAuthList,orphanFileList, userId);

        return userAuthList;
    }

    /**
     * 获取孤立点【pid不存在于fileList中的元素】
     */
    List<FileEntity> getOrphanFile(List<FileEntity> fileList) {
        List<FileEntity> orphanFileList = new ArrayList<>();
        // 获取fileIdList
        List<Long> fileIdList = new ArrayList<>();
        for (FileEntity file : fileList) {
            fileIdList.add(file.getFileId());
        }
        for (FileEntity file : fileList) {
            if (!fileIdList.contains(file.getPid())) {
                orphanFileList.add(file);
            }
        }
        return orphanFileList;
    }

    /**
     * 暴力解：使用孤立点集合递归获取一个用户的授权文件
     */
    void getUserFileAuth(List<FileAuthEntity> userAuthList,List<FileEntity> orphanFileList, Long userId) {
        for (FileEntity file : orphanFileList) {
            FileAuthEntity fileAuth = fileAuthRepository.findByFileIdAndUserId(file.getFileId(), userId);
            userAuthList.add(fileAuth);
            if (file.getIsDir()) {
                List<FileEntity> childFileList = fileRepository.getChildFileList(file.getFileId());
                recursionGetFileAuth(userAuthList, childFileList, userId, fileAuth.getPerm());
            }
        }
    }

    void recursionGetFileAuth(List<FileAuthEntity> userAuthList,List<FileEntity> childFileList, Long userId, Long perm) {
        for (FileEntity file : childFileList) {
            FileAuthEntity fileAuth = new FileAuthEntity();
            fileAuth.setFileId(file.getFileId());
            fileAuth.setUserId(userId);
            fileAuth.setPerm(perm);
            userAuthList.add(fileAuth);
            // 优化：如果该file对象是目录，则进行递归
            if (file.getIsDir()) {
                List<FileEntity> childFileList2 = fileRepository.getChildFileList(file.getFileId());
                recursionGetFileAuth(userAuthList, childFileList2, userId, fileAuth.getPerm());
            }
        }
    }

    @Override
    public boolean isRootUser(Long userId) {
        // 获取用户所拥有的权限
        // 如果权限中包含root权限，则返回true
        List<FileAuthEntity> fileAuthList = fileAuthRepository.getFileAuthByUserId(userId);
        for (FileAuthEntity fileAuth : fileAuthList) {
            if (fileAuth.getFileId() == 0) {
                return true;
            }
        }
        return false;
    }

    // 获取root用户的userIdString
    String getRootUserIdStr(){
        List<FileAuthEntity> rootUserAuthList = fileAuthRepository.selectFileAuthByFileId(0L);
        StringBuilder sb = new StringBuilder();
        for (FileAuthEntity fileAuth : rootUserAuthList) {
            sb.append(fileAuth.getUserId()).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    // 获取需要过滤的userIdString
    String getFilterUserIdStr(FileAuthEntity auth){
        String userIdStr = "";
        Long pid = Long.parseLong(auth.getParams().get("pid").toString());
        List<FileAuthEntity> fileAuthList = fileAuthRepository.selectFileAuthByFileId(pid);
        StringBuilder sb = new StringBuilder();
        for (FileAuthEntity fileAuth : fileAuthList) {
            sb.append(fileAuth.getUserId()).append(",");
        }
        userIdStr = sb.toString();
        // 加上root用户
        userIdStr += getRootUserIdStr();
        return userIdStr;
    }

    @Override
    public List<FileAuthEntity> selectFileAuthByFileId(Long fileId) {
        return fileAuthRepository.selectFileAuthByFileId(fileId);
    }

    @Override
    public List<FileAuthEntity> selectFileAuthList(FileAuthEntity FileAuthEntity) {
        List<FileAuthEntity> authList = new ArrayList<FileAuthEntity>();
        // 获取root用户list
        String filterUserIdStr = getFilterUserIdStr(FileAuthEntity);
        String sql = "select fa.file_auth_id as fileAuthId, fa.file_id as fileId, fa.user_id as userId,fa.perm, f.file_name as fileName,su.nick_name as userName " +
        "FROM file_auth fa LEFT JOIN file f on fa.file_id = f.file_id LEFT JOIN sys_user su ON fa.user_id = su.user_id where 1=1 and fa.user_id not in ("+ filterUserIdStr +") and fa.file_id != 0 ";
        if (FileAuthEntity.getFileId() != null) {
            sql += " and fa.file_id = " + FileAuthEntity.getFileId();
        }
        if (FileAuthEntity.getUserName() != null && !FileAuthEntity.getUserName().equals("")) {
            sql += " and su.nick_name like '%" + FileAuthEntity.getUserName() + "%'";
        }

        // 分页条件
        if (FileAuthEntity.getPageNum() != null && FileAuthEntity.getPageSize() != null) {
            sql += " limit " + (FileAuthEntity.getPageNum() - 1) * FileAuthEntity.getPageSize() + "," + FileAuthEntity.getPageSize();
        }
        // 执行sql语句
        List<Object[]> list = em.createNativeQuery(sql).getResultList();

        // 将查询结果转换为authList
        for (Object[] obj : list) {
            FileAuthEntity fileAuth = new FileAuthEntity();
            fileAuth.setFileAuthId(Long.parseLong(obj[0]+""));
            fileAuth.setFileId(Long.parseLong(obj[1]+""));
            fileAuth.setUserId(Long.parseLong(obj[2]+""));
            fileAuth.setPerm(Long.parseLong(obj[3]+""));
            fileAuth.setFileName(obj[4]+"");
            fileAuth.setUserName(obj[5]+"");
            authList.add(fileAuth);
        }

        return authList;
    }

    // 过滤文件权限列表
    String getFileIdStr(FileAuthEntity fileAuth) {
        Long pid = Long.parseLong(fileAuth.getParams().get("pid")+"");
        
        List<FileEntity> all = fileRepository.getChildFileList(pid);
        // 根据权限过滤【过滤出拥有管理权限的树】
        List<FileEntity> filterList = new ArrayList<>();
        List<FileAuthEntity> userAuthList = fileAuthRepository.getFileAuthByUserId(fileAuth.getUserId());
        // 如果是管理员，则不过滤
        if (isRootUser(fileAuth.getUserId())) {
            filterList = all;
        } else {
            for (FileEntity fileEntity : all) {
                for (FileAuthEntity fileAuthEntity : userAuthList) {
                    if (fileEntity.getFileId().equals(fileAuthEntity.getFileId())) {
                        filterList.add(fileEntity);
                        break;
                    }
                }
            }
        }
        List<Long> fileIdList = new ArrayList<Long>();
        for (FileEntity file : filterList) {
            fileIdList.add(file.getFileId());
        }
        fileIdList.add(pid);
        // 将fileIdList转为以逗号分隔的字符串
        String fileIdStr = fileIdList.toString().replace("[", "").replace("]", "");
        return fileIdStr;
    }


    @Override
    public long selectFileAuthCount(FileAuthEntity FileAuthEntity) {
        // 拼接sql语句
        String userIdStr = getFilterUserIdStr(FileAuthEntity);      // 获取root用户list
        String sql = "select count(*) from file_auth fa LEFT JOIN file f on fa.file_id = f.file_id LEFT JOIN sys_user su ON fa.user_id = su.user_id " +
        "where 1=1 and fa.user_id not in ("+ userIdStr +") and fa.file_id != 0 ";
        if (FileAuthEntity.getFileId() != null) {
            sql += " and fa.file_id = " + FileAuthEntity.getFileId();
        }
        if (FileAuthEntity.getUserName() != null && !FileAuthEntity.getUserName().equals("")) {
            sql += " and su.nick_name like '%" + FileAuthEntity.getUserName() + "%'";
        }
        // 执行sql语句
        Object obj = em.createNativeQuery(sql).getSingleResult();
        long count = Long.parseLong(obj.toString());
        return count;
    }

    @Override
    public int insertFileAuth(FileAuthEntity FileAuthEntity) {
        try {
            fileAuthRepository.save(FileAuthEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public int batchAddFileAuth(List<FileAuthEntity> FileAuthEntityList) {
        try {
            // 插入之前先判断是否有重复的记录，如果重复了，就修改其id并加入到list中
            List<FileAuthEntity> authList = new ArrayList<FileAuthEntity>();
            for (FileAuthEntity fileAuthEntity : FileAuthEntityList) {
                FileAuthEntity faEntity = fileAuthRepository.findByFileIdAndUserId(fileAuthEntity.getFileId(), fileAuthEntity.getUserId());
                if (faEntity == null) {
                    authList.add(fileAuthEntity);
                }else {
                    fileAuthEntity.setFileAuthId(faEntity.getFileAuthId());
                    authList.add(fileAuthEntity);
                }
            }
            fileAuthRepository.saveAll(authList);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public int updateFileAuth(FileAuthEntity FileAuthEntity) {
        try {
            fileAuthRepository.save(FileAuthEntity);
            // 同步更改子文件的权限
            List<FileEntity> childFileList = fileRepository.getChildFileList(FileAuthEntity.getFileId());
            for (FileEntity fileEntity : childFileList) {
                // 如果是文件夹，则递归更新子文件的权限
                if (fileEntity.getIsDir()) {
                    FileAuthEntity fileAuthEntity = fileAuthRepository.findByFileIdAndUserId(fileEntity.getFileId(), FileAuthEntity.getUserId());
                    fileAuthEntity.setPerm(FileAuthEntity.getPerm());
                    updateFileAuth(fileAuthEntity);
                } else {
                    FileAuthEntity fileAuthEntity = fileAuthRepository.findByFileIdAndUserId(fileEntity.getFileId(), FileAuthEntity.getUserId());
                    fileAuthEntity.setPerm(FileAuthEntity.getPerm());
                    fileAuthRepository.save(fileAuthEntity);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public int deleteFileAuthByIds(Long[] fileAuthIds) {
        try {
            // 同步删除子文件的权限
            for (Long fileAuthId : fileAuthIds) {
                FileAuthEntity fileAuthEntity = fileAuthRepository.findById(fileAuthId).get();
                if (fileAuthEntity != null) {
                    deleteFileAuth(fileAuthEntity);
                }
            }
            // fileAuthRepository.deleteAllByIdInBatch(Arrays.asList(fileAuthIds));
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    // 根据fileauthEntity删除fileauth
    void deleteFileAuth(FileAuthEntity auth){
        if (auth != null) {
            // 先把自己删了
            fileAuthRepository.delete(auth);
            // 删除子文件的权限
            List<FileEntity> childFileList = fileRepository.getChildFileList(auth.getFileId());
            for (FileEntity fileEntity : childFileList) {
                // 如果是文件夹，则递归删除子文件的权限
                if (fileEntity.getIsDir()) {
                    FileAuthEntity fileAuthEntity = fileAuthRepository.findByFileIdAndUserId(fileEntity.getFileId(), auth.getUserId());
                    deleteFileAuth(fileAuthEntity);
                } else {
                    FileAuthEntity fileAuthEntity = fileAuthRepository.findByFileIdAndUserId(fileEntity.getFileId(), auth.getUserId());
                    if (fileAuthEntity != null) {
                        fileAuthRepository.delete(fileAuthEntity);
                    }
                }
            }
        }
    }

    @Override
    public int deleteFileAuthById(Long fileAuthId) {
        try {
            fileAuthRepository.deleteById(fileAuthId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public FileAuthEntity getByFileAuthId(Long fileAuthId) {
        return fileAuthRepository.findById(fileAuthId).get();
    }

}