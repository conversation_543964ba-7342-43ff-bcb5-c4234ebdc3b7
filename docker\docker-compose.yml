services:
  chjd-disk:
    build: ./chjd-disk
    container_name: chjd-disk
    image: chjd-disk:latest
    ports:
      - "18081:8081"
    volumes:
      - /home/<USER>/disk/tmpFile/:/home/<USER>/disk/tmpFile/
      - /home/<USER>/disk/uploadPath:/home/<USER>/disk/uploadPath
    networks:
      - chjd-network
    environment:
      # mysql配置
      DB_HOST: mysql80 # MySQL容器名
      DB_PORT: 3306
      DB_NAME: chjd
      DB_USER: root
      DB_PASSWORD: Chjd@123

  chjd-web:
    build: ./chjd-disk-vue
    container_name: chjd-disk-vue
    image: chjd-web:latest
    ports:
      - "18082:80"
    volumes:
      - ./chjd-disk-vue/chjd-disk-vue/:/usr/share/nginx/html
      - ./chjd-disk-vue/nginx.conf:/etc/nginx/nginx.conf
    networks:
      - chjd-network

networks:
  chjd-network:
    external: true
