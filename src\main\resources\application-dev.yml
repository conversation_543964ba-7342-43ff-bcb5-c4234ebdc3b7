# 项目相关配置
chjd:
  # 名称
  name: chjd-disk
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 文件路径 示例（ Windows配置D:/chjd/uploadPath，Linux配置 /home/<USER>/uploadPath）
  # profile: D:/home/<USER>/uploadPath
  profile: D:/home/<USER>/uploadPath
  # 临时静态资源存储路径
  tmpFile: D:/home/<USER>/
  # oss上的网盘文件存储路径
  panFile: chjd/pan

# 数据库配置
spring:
  #数据源
  datasource:
    druid:
      #数据库连接url
      # url: ***************************************************************************************************************************************************************************************************
      # #数据库连接用户名
      # username: root
      # #数据库连接密码
      # password: root
      # url: jdbc:mysql://*************:16033/chjd?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
      url: *********************************************************************************************************************************************
      username: root
      # password: Flower@123
      password: root
  jpa:
    show-sql: true
    
logging:
  level:
    web: debug
    com.github.junrar.Archive: error
    org.hibernate.type.descriptor.sql.BasicBinder: trace
project:
  authorization:
    url: http://localhost:8880  # 测试环境
    # url: http://aftersale.charmhigh.com/chjd # 生产环境
  resource:
    #文件存储根目录
    root-path: E:/tmp/
    # root-path: /home/<USER>/
    #可用空间大小20G
    storage-total-size: 21474836480
