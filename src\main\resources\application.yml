# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
# Springboot 配置
spring:
  #数据源
  datasource:
    druid:
      #配置初始化大小/最小/最大
      initial-size: 1
      min-idle: 1
      max-active: 20
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
    properties:
      hibernate:
        dialect: com.novel.disk.common.DefaultMySQL57InnoDBDialect
        enable-lazy-load-no-trans: true
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 2000MB
  profiles:
    # active: dev
    active: prod
logging:
  level:
    web: warn
project:
  project-info:
    copyright-year: 2021
    name: novel
    version: @project.version@
    copyright-company: cnovel.club
