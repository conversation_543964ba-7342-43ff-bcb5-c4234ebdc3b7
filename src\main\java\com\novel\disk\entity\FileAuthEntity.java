package com.novel.disk.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.novel.disk.common.obj.BaseEntity;

import lombok.Data;

@Data
@Entity
@Table(name = "file_auth")
public class FileAuthEntity extends BaseEntity {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(columnDefinition = "int")
	private Long fileAuthId;

	@Column(columnDefinition = "int default 0")
	private Long fileId;
	
	@Column(columnDefinition = "int default 0")
	private Long userId;
	
	@Column(columnDefinition = "int default 0")
	private Long perm;

	@Transient
	private String fileName;

	@Transient
	private String userName;

}
