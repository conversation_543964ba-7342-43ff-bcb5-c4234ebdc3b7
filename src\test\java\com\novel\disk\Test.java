package com.novel.disk;

import com.novel.disk.common.FileUtils;

import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/7/13
 */
public class Test {
    public static void main(String[] args) throws Exception {
        InputStream is = null;
        try {
//             is = new FileInputStream();
            byte[] file = FileUtils.getBytesByFile(new File("E:\\tmp\\test\\1e8a040fd64ef9c6219c525bfaa74ed4.jpg"));
            System.out.println("len: " + file.length);
        } catch (Exception e) {
        } finally {
            if (is != null) is.close();
        }
    }
}
