# 项目相关配置
chjd:
  # 名称
  name: chjd-disk
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 文件路径 示例（ Windows配置D:/chjd/uploadPath，Linux配置 /home/<USER>/uploadPath）
  # profile: D:/home/<USER>/uploadPath
  profile: /home/<USER>/disk/uploadPath
  # 临时静态资源存储路径
#  tmpFile: D:/home/<USER>/
  tmpFile: /home/<USER>/disk/tmpFile/
  # oss上的网盘文件存储路径
  panFile: chjd/pan

# 数据库配置
spring:
  #数据源
  datasource:
    druid:
      #数据库连接url
      # url: ***************************************************************************************************************************************************************************************************
      # #数据库连接用户名
      # username: root
      # #数据库连接密码
      # password: root
    #   url: *********************************************************************************************************************************************
      url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:chjd}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
      username: ${DB_USER:root}
      password: ${DB_PASSWORD:root}
    #   password: root
  jpa:
    show-sql: true

logging:
  level:
    web: debug
    com.github.junrar.Archive: error
    org.hibernate.type.descriptor.sql.BasicBinder: trace
project:
  authorization: # 用户身份认证服务地址
    # url: http://localhost:8080  # 测试环境
    url: http://${CHJD_HOST}:${CHJD_PORT} # 生产环境
  resource:
    #文件存储根目录
    # root-path: E:/tmp/
    root-path: /home/<USER>/disk/tmp/
    #可用空间大小20G
    storage-total-size: 21474836480
