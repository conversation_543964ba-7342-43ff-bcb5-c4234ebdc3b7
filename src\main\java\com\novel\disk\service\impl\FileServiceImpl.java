package com.novel.disk.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;
import com.novel.disk.common.FileUtils;
import com.novel.disk.common.MimeTypeUtils;
import com.novel.disk.common.StringUtils;
import com.novel.disk.common.utils.oss.OssUtils;
import com.novel.disk.entity.FileAuthEntity;
import com.novel.disk.entity.FileEntity;
import com.novel.disk.entity.StorageEntity;
import com.novel.disk.entity.vo.TreeData;
import com.novel.disk.framework.config.ChjdConfig;
import com.novel.disk.framework.config.ProjectProperties;
import com.novel.disk.framework.config.Resource;
import com.novel.disk.framework.exception.BusinessException;
import com.novel.disk.repository.FileAuthRepository;
import com.novel.disk.repository.FileRepository;
import com.novel.disk.service.FileAuthService;
import com.novel.disk.service.FileService;
import com.novel.disk.service.ResourceService;
import com.novel.disk.service.StorageService;

import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import lombok.SneakyThrows;

/**
 * 文件服务实现
 *
 * <AUTHOR>
 * @date 2020/7/13
 */
@Service
public class FileServiceImpl implements FileService {
	
	private String ROOT_PATH = "/";

    private final FileRepository fileRepository;
    private final ResourceService resourceService;
    private final StorageService storageService;
    private final FileAuthRepository fileAuthRepository;
    private final FileAuthService fileAuthService;

    public FileServiceImpl(FileRepository fileRepository, ResourceService resourceService, StorageService storageService,
    ProjectProperties properties,FileAuthRepository fileAuthRepository,FileAuthService fileAuthService) {
        this.fileRepository = fileRepository;
        this.resourceService = resourceService;
        this.storageService = storageService;
        this.fileAuthRepository = fileAuthRepository;
        this.ROOT_PATH = properties.getResource().getRootPath();
        this.fileAuthService = fileAuthService;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean mkdir(String dirName, Long pid, Long userId) {
        if (StringUtils.isEmpty(dirName)) {
            throw new BusinessException("文件夹名称不能为空", 500);
        }
        String fileName = "";
        String path = "";
        if (pid != null && pid > 0) {
            Optional<FileEntity> entityOptional = fileRepository.findById(pid);
            if (entityOptional.isPresent()) {
                FileEntity fileEntity = entityOptional.get();
                if (fileEntity.getIsDir()) {
                    fileName += fileEntity.getFileName() + "/" + dirName;
                    path += fileEntity.getFilePath() + "/" + System.currentTimeMillis();
                } else {
                    fileName += "/" + dirName;
                    path += "/" + System.currentTimeMillis();
                }
            } else {
                fileName += "/" + dirName;
                path += "/" + System.currentTimeMillis();
            }
        } else {
            fileName += "/" + dirName;
            path += "/" + System.currentTimeMillis();
        }

        resourceService.mkdir(path);
        FileEntity entity = new FileEntity();
        entity.setIsDir(true);
        entity.setFileName(fileName);
        entity.setLastModifyTime(new Date());
        entity.setCreateTime(new Date());
        entity.setPid(pid);
        entity.setFilePath(path);
        entity.setUserId(userId);
        fileRepository.save(entity);
        addFileAuth(entity, userId, pid);
        return true;
    }

    @Override
    public List<FileEntity> listDir(FileEntity fileEntity) {
        Long userId = fileEntity.getUserId();
        List<FileEntity> res = new ArrayList<>();
        List<FileEntity> list = new ArrayList<>();
        if (fileEntity.getPid() != null) {
            Optional<FileEntity> optional = fileRepository.findById(fileEntity.getPid());
            if (optional.isPresent()) {
                FileEntity entity = optional.get();
                list = fileRepository.findAll(fileEntity.getPid(), fileEntity.getType(), entity.getFileName(), fileEntity.getFileName());
            }
        }
        list = fileRepository.findAll(fileEntity.getPid(), fileEntity.getType(), "", fileEntity.getFileName());

        // 处理该部分的查询结果，有两种策略，标记策略和过滤策略，目前使用标记策略
        if (!fileAuthService.isRootUser(userId)){
            // -- start --
            // 对每一项进行是否含有有权子项的标记，如果是拥有子项的，则标记为true，否则为false
            for (FileEntity item : list) {
                FileAuthEntity auth = fileAuthRepository.findByFileIdAndUserId(item.getFileId(), userId);
                if (auth != null) {
                    item.setPerm(auth.getPerm());
                } else if (hasPerm(userId, item.getFilePath())) {
                    item.setPerm(0L);
                }
            }
        }
        res = list;

        return res;
    }

    // 判断是否有有权子项
    private boolean hasPerm(Long userId, String filePath) {
        List<FileEntity> fileList =  fileRepository.findAllByFilePathIsStartingWith(filePath);
        List<FileAuthEntity> authList = fileAuthRepository.getFileAuthByUserId(userId);
        // 将两个list进行循环比较，使用authlist进行过滤
        for(FileEntity file : fileList){
            for(FileAuthEntity auth : authList){
                if(file.getFileId().equals(auth.getFileId())){
                    return true;
                }
            }
        }
        return false;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean upLoad(@NotNull MultipartFile file, Long pid, Long userId) {
        if (file == null) {
            return false;
        }
        
        String filename = file.getOriginalFilename();
        String md5 = FileUtils.getMD5(file.getBytes());
        String fileSuffix = FileUtils.getFileSuffix(Objects.requireNonNull(filename));
        String fileName = "", filePath = "";
        if (pid != null && pid > 0) {
            Optional<FileEntity> dir = fileRepository.findById(pid);
            if (dir.isPresent()) {
                FileEntity fileEntity = dir.get();
                if (fileEntity.getIsDir()) {
                    fileName += fileEntity.getFileName() + "/" + filename;
                    filePath += fileEntity.getFilePath() + "/" + md5 + "." + fileSuffix;
                } else {
                    fileName += "/" + filename;
                    filePath += "/" + md5 + "." + fileSuffix;
                }
            } else {
                fileName += "/" + filename;
                filePath += "/" + md5 + "." + fileSuffix;
            }
        } else {
            fileName += "/" + filename;
            filePath += "/" + md5 + "." + fileSuffix;
        }

        resourceService.upLoad(file.getInputStream(), filePath);
        FileEntity fileEntity = new FileEntity();
        fileEntity.setPid(pid);
        fileEntity.setFilePath(filePath);
        fileEntity.setCreateTime(new Date());
        fileEntity.setLastModifyTime(new Date());
        fileEntity.setFileName(fileName);
        fileEntity.setIsDir(false);
        fileEntity.setHash(md5);
        fileEntity.setExtendName(fileSuffix);
        fileEntity.setFileSize(file.getSize());
        fileEntity.setDownloadNum(0);
        fileEntity.setUserId(userId);
        fileEntity.setType(MimeTypeUtils.getFileType(fileSuffix));
        fileRepository.save(fileEntity);
        synchronized (FileServiceImpl.class) {
            // StorageEntity storage = storageService.getStorageByUserId(userId);
            StorageEntity storage = storageService.getStorage();
            if (storage == null) {
                storage = new StorageEntity();
                storage.setUsedSize(0L);
                storage.setTotalSize(Resource.getStorageTotalSize());
                storage.setUserId(userId);
            }
            if (fileEntity.getFileSize() != null) {
                storage.setUsedSize(storage.getUsedSize() + fileEntity.getFileSize());
            }
            storageService.updateStorage(storage);
            addFileAuth(fileEntity, userId, pid);
        }
        return true;
    }

    @Override
    public JSONObject upLoads(MultipartFile[] files, Long pid, Long userId) {
        JSONObject res = new JSONObject();
        for (MultipartFile file : files) {
            // 输出文件名
            String filename = file.getOriginalFilename();
            if (upLoad(file, pid, userId)) {
                res.put("flag", true);
            } else {
                res.put("flag", false);
            }
        }
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long[] sourceListId, Long userId) {
        List<FileEntity> fileEntityList = fileRepository.findAllById(Arrays.asList(sourceListId));

        Long fileSize = 0L;
        List<Long> fileIdList = new ArrayList<>();
        for (FileEntity fileEntity : fileEntityList) {
            resourceService.remove(fileEntity.getFilePath(), fileEntity.getIsDir());
            // 
            List<FileEntity> entities = fileRepository.findAllByFilePathIsStartingWith(fileEntity.getFilePath());
            for (FileEntity entity : entities) {
                fileIdList.add(entity.getFileId());
                if (entity.getFileSize() != null) {
                    fileSize += entity.getFileSize();
                }
            }
            fileRepository.deleteAllInBatch(entities);
        }
        // 删除文件权限信息
        List<FileAuthEntity> authList = fileAuthRepository.selectFileAuthByFileIdList(fileIdList);
        fileAuthRepository.deleteAllInBatch(authList);
        synchronized (FileServiceImpl.class) {
            // StorageEntity storage = storageService.getStorageByUserId(userId);
            StorageEntity storage = storageService.getStorage();
            if (storage == null) {
                storage = new StorageEntity();
                storage.setUsedSize(0L);
                storage.setTotalSize(Resource.getStorageTotalSize());
                storage.setUserId(userId);
            }
            storage.setUsedSize(storage.getUsedSize() - fileSize);
            storageService.updateStorage(storage);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean rename(Long id, String name, Long userId) {
        if (StringUtils.isEmpty(name)) {
            throw new BusinessException("文件名称不能为空", 500);
        }
        Optional<FileEntity> optionalFileEntity = fileRepository.findById(id);
        if (optionalFileEntity.isPresent()) {
            FileEntity fileEntity = optionalFileEntity.get();
            FileEntity pFileEntity = fileEntity.getPFileEntity();
            String pname = pFileEntity == null ? "" : pFileEntity.getFileName();
            if (!fileEntity.getIsDir()) {
                name = pname + "/" + name + "." + fileEntity.getExtendName();
            } else {
                name = pname + "/" + name;
                // 更新子文件/文件夹名称
                List<FileEntity> fileEntities = fileRepository.findAllByFilePathIsStartingWith(fileEntity.getFilePath());
                for (FileEntity entity : fileEntities) {
                    // 排除自己
                    if (entity.getFileId().equals(fileEntity.getFileId())) {
                        continue;
                    }
                    renameChilds(entity, fileEntity.getFileName(), name);
                }
            }
            fileEntity.setFileName(name);
            fileRepository.save(fileEntity);
        }else{
            throw new BusinessException("文件不存在", 500);
        }
        return true;
    }

    void renameChilds(FileEntity fileEntity, String oldPname, String pname) {
        String cname = fileEntity.getFileName();
        String suffix = cname.substring(cname.indexOf(oldPname)+oldPname.length(), cname.length());
        fileEntity.setFileName(pname + suffix);
        fileRepository.save(fileEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean copyDir(Long[] sourceListId, Long targetId, Long userId) {
        List<FileEntity> fileEntityList = fileRepository.findAllById(Arrays.asList(sourceListId));
        AtomicReference<Long> fileSize = new AtomicReference<>(0L);
        //判断这些文件夹文件的大小
        for (FileEntity fileEntity : fileEntityList) {
            List<FileEntity> fileEntities = fileRepository.findAllByFilePathIsStartingWith(fileEntity.getFilePath());
            fileEntities.forEach(it -> {
                if (it.getFileSize() != null) {
                    fileSize.updateAndGet(v -> v + it.getFileSize());
                }
            });
        }

        StorageEntity storage = storageService.getStorage();
        if (fileSize.get() > (storage.getTotalSize() - storage.getUsedSize())) {
            throw new BusinessException("存储空间不足！", 500);
        }
        if (targetId == 0) {
            FileEntity fileEntity = new FileEntity();
            fileEntity.setFileId(targetId);
            fileEntity.setUserId(userId);
            copyDir(fileEntityList, fileEntity, userId);
        } else {
            Optional<FileEntity> entityOptional = fileRepository.findById(targetId);
            entityOptional.ifPresent(fileEntity -> copyDir(fileEntityList, fileEntity, userId));
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean moveDir(Long[] sourceListId, Long targetId, Long userId) {
        List<FileEntity> fileEntityList = fileRepository.findAllById(Arrays.asList(sourceListId));
        if (targetId == 0) {
            FileEntity fileEntity = new FileEntity();
            fileEntity.setFileId(targetId);
            fileEntity.setUserId(userId);
            moveDir(fileEntityList, fileEntity, userId);
        } else {
            Optional<FileEntity> entityOptional = fileRepository.findById(targetId);
            entityOptional.ifPresent(fileEntity -> moveDir(fileEntityList, fileEntity, userId));
        }
        return true;
    }


    @Override
    public byte[] download(Long fileId) {
        Optional<FileEntity> entityOptional = fileRepository.findById(fileId);
        if (entityOptional.isPresent()) {
            FileEntity entity = entityOptional.get();
            entity.setDownloadNum(entity.getDownloadNum() != null && entity.getDownloadNum() >= 0 ? entity.getDownloadNum() + 1 : 1);
            fileRepository.save(entity);
            return resourceService.download(entity);
        }
        return null;
    }

    @Override
    public File readBytes(String path) {
        return resourceService.readBytes(path);
    }

    @Override
    public TreeData listDirTree(Long userId) {
        FileEntity fileEntity = new FileEntity();
        fileEntity.setIsDir(true);
        // fileEntity.setUserId(userId);
        // 根据userId查询用户权限
        List<FileAuthEntity> authList = fileAuthRepository.getFileAuthByUserId(userId);
        List<FileEntity> all = fileRepository.findAll(Example.of(fileEntity));
        // 根据用户权限过滤文件夹
        List<FileEntity> fileEntities = new ArrayList<>();
        for (FileEntity entity : all) {
            for (FileAuthEntity authEntity : authList) {
                if (authEntity.getFileId().equals(entity.getFileId())) {
                    fileEntities.add(entity);
                    break;
                }
            }
        }
        TreeData treeData = new TreeData();
        treeData.setId(0L);
        treeData.setLabel("/");
        treeData.setChildren(getTreeData(fileEntities, 0));
        return treeData;
    }

    @Override
    public FileEntity getFileById(Long fileId) {
        return fileRepository.findById(fileId).orElse(null);
    }

    @Override
    public FileEntity getDownloadUrl(Long fileId) {
        Optional<FileEntity> entityOptional = fileRepository.findById(fileId);
        if (entityOptional.isPresent()) {
            FileEntity fileEntity = entityOptional.get();
            if (fileEntity.getIsDir()) {
                String path = resourceService.zip(fileEntity);
                fileEntity.setUrl(path);
            } else {
                fileEntity.setUrl(fileEntity.getFilePath());
            }
            return fileEntity;
        }
        return null;
    }

    @Override
    public boolean unzip(Long fileId, Long userId) {
        Optional<FileEntity> entityOptional = fileRepository.findById(fileId);
        if (entityOptional.isPresent()) {
            FileEntity fileEntity = entityOptional.get();
            // StorageEntity storage = storageService.getStorageByUserId(userId);
            StorageEntity storage = storageService.getStorage();
            if (storage == null || fileEntity.getFileSize() > (storage.getTotalSize() - storage.getUsedSize())) {
                throw new BusinessException("存储空间不足！", 500);
            }
            synchronized (FileServiceImpl.class) {
                //1.解压
                File unzip = resourceService.unzip(fileEntity);

                if (unzip == null) {
                    return false;
                }
                //2.扫描文件，添加到数据库
                FileEntity entity = getFileEntity(fileEntity, unzip, userId);
                entity.setPid(fileEntity.getPid());
                fileRepository.save(entity);
                resourceService.remove(unzip);

                if (fileEntity.getFileSize() != null) {
                    storage.setUsedSize(storage.getUsedSize() + fileEntity.getFileSize());
                }
                storageService.updateStorage(storage);
            }
        }
        return true;
    }

    @SneakyThrows
    public FileEntity getFileEntity(FileEntity fileEntity, File unzip, Long userId) {
        FileEntity entity = new FileEntity();
        entity.setDownloadNum(0);

        entity.setUserId(userId);
        //父目录的路径  父目录名称
        String pPath = "", pName = "";

        if (fileEntity != null && fileEntity.getIsDir()) {
            pPath += fileEntity.getFilePath();
            pName += fileEntity.getFileName();
        } else if (fileEntity != null && !fileEntity.getIsDir()) {
            //如果目录时文件，那么目录就应该为文件的上级目录
            pName += fileEntity.getFileName().replace(fileEntity.getName(), "");
            pPath += fileEntity.getFilePath().replace(fileEntity.getPath(), "");
        }

        if (unzip.isDirectory()) {
            entity.setIsDir(true);
            File[] files = unzip.listFiles();

            //当前新目录的路径
            String filePath = pPath + "/" + System.currentTimeMillis();
            //当前父目录名称
            String fileName;
            if (fileEntity != null && !fileEntity.getIsDir()) {
                String name = fileEntity.getName();
                fileName = pName + "/" + name.substring(0, name.lastIndexOf("."));
            } else {
                fileName = pName + "/" + unzip.getName();
            }


            //创建目录
            resourceService.mkdir(filePath);
            entity.setFilePath(filePath);
            entity.setFileName(fileName);

            if (files != null && files.length > 0) {
                ArrayList<FileEntity> fileEntities = new ArrayList<>(files.length);
                for (File file : files) {
                    FileEntity fileEntity1 = getFileEntity(entity, file, userId);
                    fileEntities.add(fileEntity1);
                }
                entity.setFileEntityList(fileEntities);
            }
        } else {
            String fileSuffix = FileUtils.getFileSuffix(Objects.requireNonNull(unzip.getName()));
            String md5 = FileUtils.getMD5(unzip);
            //当前新目录的路径
            String filePath = pPath + "/" + md5 + "_" + unzip.getName() + "." + fileSuffix;
            //当前父目录名称
            String fileName = pName + "/" + unzip.getName();

            entity.setFilePath(filePath);
            entity.setFileName(fileName);

            entity.setIsDir(false);
            entity.setHash(md5);

            entity.setFileSize(unzip.length());

            entity.setType(MimeTypeUtils.getFileType(fileSuffix));
            entity.setExtendName(fileSuffix);
            //移动文件到指定目录
            resourceService.moveDir(unzip, filePath);
        }
        return entity;
    }


    /**
     * 树结构
     *
     * @param list 列表
     * @param pid  父级id
     * @return 结果
     */
    private List<TreeData> getTreeData(List<FileEntity> list, long pid) {
        List<TreeData> returnList = new ArrayList<>();
        for (FileEntity t : list) {
            if (t.getPid() == pid) {
                TreeData treeData = new TreeData();
                treeData.setId(t.getFileId());
                treeData.setLabel(t.getName());
                returnList.add(treeData);
                List<TreeData> child = getTreeData(list, t.getFileId());
                treeData.setChildren(child);
            }
        }
        return returnList;
    }

    /**
     * 复制
     *
     * @param fileEntityList   复制文件列表
     * @param targetFileEntity 目标目录
     * @param userId           用户id
     * @return 结果
     */
    private boolean copyDir(List<FileEntity> fileEntityList, FileEntity targetFileEntity, Long userId) {
        Long fileSize = 0L;
        for (FileEntity fileEntity : fileEntityList) {
            FileEntity pFileEntity = copy(targetFileEntity, fileEntity, userId);
            if (fileEntity.getIsDir()) {
                //文件夹，递归复制
                if (pFileEntity != null) {
                    //把对应的文件移动到当前目录下
                    copyDir(fileEntity.getFileEntityList(), pFileEntity, userId);
                }
            } else {
                //文件则直接复制
                if (pFileEntity != null && pFileEntity.getFileSize() != null) {
                    fileSize += pFileEntity.getFileSize();
                }
            }
        }
        synchronized (FileServiceImpl.class) {
            // StorageEntity storage = storageService.getStorageByUserId(userId);
            StorageEntity storage = storageService.getStorage();
            if (storage == null) {
                storage = new StorageEntity();
                storage.setUsedSize(0L);
                storage.setTotalSize(Resource.getStorageTotalSize());
                storage.setUserId(userId);
            }
            storage.setUsedSize(storage.getUsedSize() + fileSize);
            storageService.updateStorage(storage);
        }
        return true;
    }

    /**
     * 复制
     *
     * @param targetFileEntity 目标目录
     * @param fileEntity       文件
     * @return 结果
     */
    private FileEntity copy(FileEntity targetFileEntity, FileEntity fileEntity, Long userId) {
        FileEntity newFileEntity = new FileEntity();
        BeanUtils.copyProperties(fileEntity, newFileEntity, "fileName", "pid", "filePath", "fileId", "fileEntityList", "pFileEntity");
        String filePath = targetFileEntity.getFilePath() == null ? "/" + fileEntity.getPath() : targetFileEntity.getFilePath() + "/" + fileEntity.getPath();
        String fileName = targetFileEntity.getFileName() == null ? "/" + fileEntity.getName() : targetFileEntity.getFileName() + "/" + fileEntity.getName();
        newFileEntity.setPid(targetFileEntity.getFileId());
        resourceService.copyDir(ChjdConfig.getPanFile() + fileEntity.getFilePath(),ChjdConfig.getPanFile() + filePath);
        newFileEntity.setFilePath(filePath);
        newFileEntity.setFileName(fileName);
        FileEntity pFileEntity = fileRepository.save(newFileEntity);
        addFileAuth(pFileEntity, userId, targetFileEntity.getFileId()); //添加文件权限
        return pFileEntity;
    }

    /**
     * 移动文件
     *
     * @param fileEntityList   文件列表
     * @param targetFileEntity 目标目录
     * @param userId           用户id
     * @return 结果
     */
    private boolean moveDir(List<FileEntity> fileEntityList, FileEntity targetFileEntity, Long userId) {
        for (FileEntity fileEntity : fileEntityList) {
            if (fileEntity.getIsDir()) {
                //文件夹，递归移动
                move(targetFileEntity, fileEntity, userId);
                //把对应的文件移动到当前目录下
                moveDir(fileEntity.getFileEntityList(), fileEntity, userId);
            } else {
                //文件则直接移动
                move(targetFileEntity, fileEntity, userId);
            }
        }
        return true;
    }

    private void move(FileEntity targetFileEntity, FileEntity fileEntity, Long userId) {
        // 修改对应的文件名和文件路径
        String filePath = targetFileEntity.getFilePath() == null ? "/" + fileEntity.getPath() : targetFileEntity.getFilePath() + "/" + fileEntity.getPath();
        String fileName = targetFileEntity.getFileName() == null ? "/" + fileEntity.getName() : targetFileEntity.getFileName() + "/" + fileEntity.getName();
        fileEntity.setPid(targetFileEntity.getFileId());
        resourceService.moveDir(ChjdConfig.getPanFile() + fileEntity.getFilePath(),ChjdConfig.getPanFile() + filePath);
        fileEntity.setFilePath(filePath);
        fileEntity.setFileName(fileName);
        fileRepository.saveAndFlush(fileEntity);
    }


    /**
     * 网盘文件下载
     */
    @SneakyThrows
	@Override
	public void fileDownload(HttpServletResponse resp, Long fileId , Long userId) {
		Optional<FileEntity> entityOptional = fileRepository.findById(fileId);
		if (entityOptional.isPresent()) {
			FileEntity fileEntity = entityOptional.get();
            // 下载次数+1
            fileEntity.setDownloadNum(fileEntity.getDownloadNum() + 1);
            fileRepository.save(fileEntity);
			if (!fileEntity.getIsDir()) {
				OssUtils.downloadFromOss(resp, ChjdConfig.getPanFile() + fileEntity.getFilePath(), fileEntity.getFileName());
			}else {
                // 收集文件夹下的所有文件作为filePaths
                List<HashMap<String,Object>> fileAttrs = new ArrayList<>();
                // 列出所有相关文件
		        List<FileEntity> all = fileRepository.findAllByFilePathIsStartingWith(fileEntity.getFilePath());
                // 递归收集所有文件
				collectFilePathsRecursion(all, fileAttrs, fileEntity.getFileId(), ChjdConfig.getPanFile());
                // 将所有文件下载
                OssUtils.downloadZipFromOss(resp, fileAttrs, fileEntity.getFileName()+".zip");
			}
		}
	}
	
	/**
	 *  收集文件夹下的所有文件作为filePaths
	 * @param fileEntity
	 */
	void collectFilePathsRecursion(List<FileEntity> list, List<HashMap<String,Object>> fileAttrs, Long pid, String prefix) {
        for (FileEntity t : list) {
            if (t.getPid() == pid) {
            	if (!t.getIsDir()) {
                    fileAttrs.add(new HashMap<String, Object>() {{
                        put("filePath", prefix + t.getFilePath());
                        put("fileName", t.getFileName().substring(t.getFileName().lastIndexOf("/")));
                    }});
				}else {
					collectFilePathsRecursion(list, fileAttrs, t.getFileId(), prefix);
				}
            }
        }
	}

    @Override
    public TreeData listFileTree(FileEntity fEntity) {
        Long userId = fEntity.getUserId();
        Long pid = fEntity.getPid() != null ? fEntity.getPid() : 0L;
        String label = "/";
        if (pid != 0) {
            String fileName = fileRepository.findById(pid).get().getFileName();
            label = fileName == null ? "/" : fileName.substring(fileName.lastIndexOf("/")+1, fileName.length());
        }
        List<FileEntity> all = fileRepository.findAll();
        // 根据权限过滤【过滤出拥有管理权限的树】
        List<FileEntity> list = new ArrayList<>();
        List<FileAuthEntity> authList = fileAuthRepository.getFileAuthByUserId(userId);
        // 如果是管理员，则不过滤
        if (fileAuthService.isRootUser(userId)) {
            list = all;
        } else {
            for (FileEntity fileEntity : all) {
                for (FileAuthEntity fileAuthEntity : authList) {
                    if (fileEntity.getFileId().equals(fileAuthEntity.getFileId())) {
                        list.add(fileEntity);
                        break;
                    }
                }
            }
        }
        // 构建树
        TreeData treeData = new TreeData();
        treeData.setId(pid);
        treeData.setLabel(label);
        treeData.setChildren(getTreeData(list, pid));
        return treeData;
    }

    /**
     * 新建文件/文件夹授权给自己和拥有父目录权限的用户
     */
    public void addFileAuth(FileEntity fileEntity, Long userId, Long pid) {
        // 新建文件(夹)默认授权给自己和拥有父目录权限的用户
        FileAuthEntity authEntity = new FileAuthEntity();
        authEntity.setFileId(fileEntity.getFileId());
        authEntity.setUserId(userId);
        authEntity.setPerm(4L);
        fileAuthRepository.save(authEntity);
        // 授权给父目录的用户
        if (pid != null && pid > 0) {
            // 找出父目录相关记录，利用记录的userIdList创建新的权限记录并保存，只需要改变文件id，权限使用继承的
            List<FileAuthEntity> authList = fileAuthRepository.selectFileAuthByFileId(pid);
            for(FileAuthEntity auth : authList){
                // 排除自己
                if(auth.getUserId().equals(userId)){
                    continue;
                }
                FileAuthEntity newAuth = new FileAuthEntity();
                newAuth.setFileId(fileEntity.getFileId());
                newAuth.setUserId(auth.getUserId());
                newAuth.setPerm(auth.getPerm());
                fileAuthRepository.save(newAuth);
            }
        }
    }
}
