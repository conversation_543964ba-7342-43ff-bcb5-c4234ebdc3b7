#!/bin/bash
#docker部分

echo '================打包完成，开始制作镜像================'

echo '================停止容器 diskapi================'
docker stop diskapi
echo '================删除容器 diskapi================'
docker rm diskapi
echo '================删除镜像 diskapi:last================'
docker rmi diskapi:last
echo '================修改上一次的镜像名 diskapi:latest================'
docker tag diskapi:latest diskapi:last
echo '================build 镜像 diskapi:latest================'
docker build -t diskapi:latest  .
# echo '================运行容器 diskapi================'
# docker run --name=diskapi --restart always -d -p 8883:8888 -v /data:/disk -v /logs:/logs diskapi:latest

# echo "finished!"
# echo '================部署完成================'
