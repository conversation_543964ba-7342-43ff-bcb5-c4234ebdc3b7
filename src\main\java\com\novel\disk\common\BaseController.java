package com.novel.disk.common;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.novel.disk.common.constant.HttpStatus;
import com.novel.disk.common.core.page.PageDomain;
import com.novel.disk.common.core.page.TableDataInfo;
import com.novel.disk.common.core.page.TableSupport;
import com.novel.disk.common.obj.AjaxResult;
import com.novel.disk.common.utils.SqlUtil;
import com.novel.disk.entity.SysUser;

import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 * @date 2020/7/13
 */
public class BaseController {

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotEmpty(pageDomain.getOrderBy()))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected TableDataInfo getDataTable(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 响应返回结果
     * 
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows)
    {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     * 
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjaxRes(boolean result)
    {
        return result ? AjaxResult.success() : AjaxResult.error();
    }


    /**
     * 响应返回结果
     *
     * @param result 影响结果
     * @return 操作结果
     */
    protected Result toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 响应返回结果
     *
     * @param data 数据
     * @return 操作结果
     */
    protected Result toAjax(Object data) {
        return Result.success(data);
    }

    /**
     * 响应返回结果
     *
     * @param result  影响结果
     * @param success 成功消息
     * @return 操作结果
     */
    protected Result toAjax(boolean result, String success) {
        return result ? success(success) : error();
    }

    /**
     * 响应返回结果
     *
     * @param result  影响结果
     * @param success 成功消息
     * @param error   失败结果
     * @return 操作结果
     */
    protected Result toAjax(boolean result, String success, String error) {
        return result ? success(success) : error(error);
    }

    /**
     * 返回成功
     */
    protected Result success() {
        return Result.success();
    }

    /**
     * 返回失败消息
     */
    protected Result error() {
        return Result.error();
    }

    /**
     * 返回成功消息
     */
    protected Result success(String message) {
        return Result.success(message);
    }

    /**
     * 返回失败消息
     */
    protected Result error(String message) {
        return Result.error(message);
    }

    /**
     * 返回错误码消息
     */
    protected Result error(int code, String message) {
        return Result.error(message, code);
    }

    /**
     * 获取当前用户
     *
     * @return 用户
     */
    protected SysUser getUser() {
        Object user = ServletUtils.getRequest().getAttribute("user");
        if (user instanceof SysUser) {
            return (SysUser) user;
        }
        return null;
    }

    /**
     * 获取当前用户id
     *
     * @return 用户id
     */
    protected Long getUserId() {
        if (getUser() != null) {
            return getUser().getId();
        }
        return null;
    }

    /**
     * 获取当前用户用户名
     *
     * @return 用户名
     */
    protected String getUserName() {
        if (getUser() != null) {
            return getUser().getUserName();
        }
        return null;
    }

}
