package com.novel.disk.repository;

import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

import com.novel.disk.entity.FileAuthEntity;

public interface FileAuthRepository extends JpaRepository<FileAuthEntity, Long>, JpaSpecificationExecutor<FileAuthEntity>  {

    /**
     * 根据userId查询文件权限
     * @param userId
     * @return
     */
    @Query(value = "select * from file_auth where user_id = ?1", nativeQuery = true)
    List<FileAuthEntity> getFileAuthByUserId(Long userId);

     /**
     * 根据文件ID查询文件权限
     * 
     * @param fileId 文件ID
     * @return 文件权限
     */
    @Query(value = "select * from file_auth where file_id = ?1", nativeQuery = true)
    public List<FileAuthEntity> selectFileAuthByFileId(Long fileId);

    /**
     * 根据文件IDList查询文件权限
     */
    @Query(value = "select * from file_auth where file_id in ?1", nativeQuery = true)
    public List<FileAuthEntity> selectFileAuthByFileIdList(@Param("fileIdList") List<Long> fileIdList);

    /**
     * 根据文件id和userId查询文件权限
     */
    @Query(value = "select * from file_auth where file_id = ?1 and user_id = ?2 limit 1", nativeQuery = true)
    FileAuthEntity findByFileIdAndUserId(Long fileId, Long userId);

    /**
     * 根据文件id删除文件权限
     */
    @Query(value = "delete from file_auth where file_id = ?1", nativeQuery = true)
    public void deleteByFileId(Long fileId);

}
