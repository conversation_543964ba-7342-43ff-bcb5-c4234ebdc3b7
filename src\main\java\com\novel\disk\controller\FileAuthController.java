package com.novel.disk.controller;

import java.util.HashMap;
import java.util.List;

import com.novel.disk.common.BaseController;
import com.novel.disk.common.annotation.Log;
import com.novel.disk.common.constant.HttpStatus;
import com.novel.disk.common.core.page.TableDataInfo;
import com.novel.disk.common.enums.BusinessType;
import com.novel.disk.common.obj.AjaxResult;
import com.novel.disk.entity.FileAuthEntity;
import com.novel.disk.entity.FileEntity;
import com.novel.disk.service.FileAuthService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AllArgsConstructor;

/**
 * 文件权限控制器
 */
@RestController
@RequestMapping("/fileAuth")
@AllArgsConstructor
public class FileAuthController extends BaseController {

	private final FileAuthService fileAuthService;

	/**
	 * 根据id查询用户所拥有的权限
	 *
	 * @param fileEntity 查询条件
	 * @return 结果
	 */
	@GetMapping("/getFileAuthByUserId")
	public AjaxResult getFileAuthByUserId(FileEntity fileEntity) {
        return AjaxResult.success(fileAuthService.getFileAuthByUserId(getUserId()));
	}

    /**
	 * 根据id查询用户所拥有的权限
	 *
	 * @param fileEntity 查询条件
	 * @return 结果
	 */
	@GetMapping("/getByFileAuthId/{fileAuthId}")
	public AjaxResult getByFileAuthId(@PathVariable("fileAuthId") Long fileAuthId) {
        return AjaxResult.success(fileAuthService.getByFileAuthId(fileAuthId));
	}

	/**
	 * 查询文件权限列表
	 */
	@GetMapping("/list")
	public TableDataInfo list(FileAuthEntity fileAuth)
    {
        fileAuth.setUserId(getUserId());
        List<FileAuthEntity> list = fileAuthService.selectFileAuthList(fileAuth);
        long count = fileAuthService.selectFileAuthCount(fileAuth);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(count);
        return rspData;
    }

    /**
     * 根据文件Id查询文件权限列表
     */
    @GetMapping("/selectFileAuthByFileId")
    public TableDataInfo selectFileAuthByFileId(Long fileId)
    {
        startPage();
        List<FileAuthEntity> list = fileAuthService.selectFileAuthByFileId(fileId);
        return getDataTable(list);
    }

    /**
     * 新增文件权限
     */
    @Log(title = "企业网盘", businessType = BusinessType.ADD_PERMISSION)
    @PostMapping
    public AjaxResult add(@RequestBody FileAuthEntity fileAuth)
    {
		return toAjax(fileAuthService.insertFileAuth(fileAuth));
    }

    /**
     * 批量新增文件权限
     */
    @Log(title = "企业网盘", businessType = BusinessType.ADD_PERMISSION)
    @PostMapping("/batchAddFileAuth")
    public AjaxResult batchAddFileAuth(@RequestBody List<FileAuthEntity> fileAuthList)
    {
        return toAjax(fileAuthService.batchAddFileAuth(fileAuthList));
    }

    /**
     * 修改文件权限
     */
    @Log(title = "企业网盘", businessType = BusinessType.MODIFY_PERMISSION)
    @PutMapping
    public AjaxResult edit(@RequestBody FileAuthEntity fileAuth)
    {
        return toAjax(fileAuthService.updateFileAuth(fileAuth));
    }

    /**
     * 删除文件权限
     */
    @Log(title = "企业网盘", businessType = BusinessType.DELETE_PERMISSION)
	@DeleteMapping("/{fileAuthIds}")
    public AjaxResult remove(@PathVariable Long[] fileAuthIds)
    {
        return toAjax(fileAuthService.deleteFileAuthByIds(fileAuthIds));
    }

}