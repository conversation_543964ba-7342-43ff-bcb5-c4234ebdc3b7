2021-12-21 11:43:26.050 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:43:26,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:44:35.471 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:44:35,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:45:23.022 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:45:23,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:45:55.528 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:45:55,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:45:55.559 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:45:55,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:47:53.111 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:47:53,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:47:57.781 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:47:57,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:48:00.674 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:48:00,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:48:01.981 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:48:01,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-21 11:48:04.734 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-21 11:48:04,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
