package com.novel.disk.manager.factory;

import java.util.Date;
import java.util.TimerTask;

import com.novel.disk.common.utils.AddressUtils;
import com.novel.disk.common.utils.SpringUtils;
import com.novel.disk.entity.PanLog;
import com.novel.disk.entity.SysOperLog;
import com.novel.disk.repository.LogRepository;
import com.novel.disk.repository.SysOperLogRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 异步工厂（产生任务用）
 * 
 * <AUTHOR>
 */
public class AsyncFactory
{
    private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

    /**
     * 记录登录信息
     * 
     * @param username 用户名
     * @param status 状态
     * @param message 消息
     * @param args 列表
     * @return 任务task
     */
    // public static TimerTask recordLogininfor(final String username, final String status, final String message, final Object... args){
        
    // }

    /**
     * 操作日志记录
     * 
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final SysOperLog operLog)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
                operLog.setOperTime(new Date());
                SpringUtils.getBean(SysOperLogRepository.class).save(operLog);
            }
        };
    }

    /**
     * 记录网盘操作日志
     * 
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordDiskOper(final PanLog log)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                log.setOperTime(new Date());
                SpringUtils.getBean(LogRepository.class).save(log);
            }
        };
    }
}
