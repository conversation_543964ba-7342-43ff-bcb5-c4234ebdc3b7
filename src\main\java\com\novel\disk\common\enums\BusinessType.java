package com.novel.disk.common.enums;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 */
public enum BusinessType
{
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,
    
    /**
     * 清空数据
     */
    CLEAN,
    
    /**
     * 上传文件
     */
    UPLOAD,
    
    /**
     * 下载文件
     */
    DOWNLOAD,

    /**
     * 新建目录
     */
    MKDIR,

    /**
     * 重命名
     */
    RENAME,

    /**
     * 删除文件
     */
    DELETE_FILE,

    /**
     * 添加权限
     */
    ADD_PERMISSION,

    /**
     * 修改权限
     */
    MODIFY_PERMISSION,

    /**
     * 删除权限
     */
    DELETE_PERMISSION,

    /**
     * 移动文件
     */
    MOVE_DIR,

    /**
     * 复制文件
     */
    COPY_DIR,
}
