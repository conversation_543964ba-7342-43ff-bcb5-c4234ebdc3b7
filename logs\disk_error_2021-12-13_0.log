2021-12-13 09:16:11.398 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF (0 is not null,f.pid = 0,1=1) AND IF (0 !=0,f.type = 0,1=1)  AND IF ('' !='',' at line 1
2021-12-13 09:16:11.471 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:16:11,-1,未知错误could not extract ResultSet; SQL [n/a]; nested exception is org.hibernate.exception.SQLGrammarException: could not extract ResultSet
2021-12-13 09:17:27.565 default [http-nio-8884-exec-2] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ') ORDER BY is_dir DESC' at line 1
2021-12-13 09:17:27.572 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:17:27,-1,未知错误could not extract ResultSet; SQL [n/a]; nested exception is org.hibernate.exception.SQLGrammarException: could not extract ResultSet
2021-12-13 09:19:29.028 default [http-nio-8884-exec-3] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ') ORDER BY is_dir DESC' at line 1
2021-12-13 09:19:29.033 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:19:29,-1,未知错误could not extract ResultSet; SQL [n/a]; nested exception is org.hibernate.exception.SQLGrammarException: could not extract ResultSet
2021-12-13 09:22:28.023 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:22:28,-1,未知错误[Ljava.lang.Object; cannot be cast to com.novel.disk.entity.FileEntity
2021-12-13 09:23:38.053 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:23:38,-1,未知错误Could not write JSON: (was java.lang.NullPointerException); nested exception is com.fasterxml.jackson.databind.JsonMappingException: (was java.lang.NullPointerException) (through reference chain: com.novel.disk.common.Result["data"]->java.util.ArrayList[0]->com.novel.disk.entity.FileEntity["name"])
2021-12-13 09:54:30.621 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 09:54:30,-1,未知错误null
2021-12-13 10:24:06.633 default [http-nio-8884-exec-7] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/test/1/售后资料整理.md' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 10:24:06.697 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 10:24:06,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 11:13:55.301 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/test/1/长沙常衡机电全自动视觉贴片机（CHM-T530P�' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 11:13:55.355 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 11:13:55,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:33.429 default [http-nio-8884-exec-2] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:33.511 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:33,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:33.566 default [http-nio-8884-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:33.621 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:33,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:34.451 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:34.496 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:34,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:34.779 default [http-nio-8884-exec-3] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:34.833 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:34,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:36.650 default [http-nio-8884-exec-4] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:36.671 default [http-nio-8884-exec-6] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:36.697 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:36,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:36.716 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:36,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:37.717 default [http-nio-8884-exec-7] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:37.764 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:37,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:38.017 default [http-nio-8884-exec-5] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:38.047 default [http-nio-8884-exec-8] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:38.071 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:38,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:38.093 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:38,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:38.557 default [http-nio-8884-exec-10] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:38.605 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:38,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:40.406 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:40.453 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:40,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:40.504 default [http-nio-8884-exec-2] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:40.558 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:40,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:40.569 default [http-nio-8884-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:40.614 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:40,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:41.233 default [http-nio-8884-exec-3] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:41.279 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:41,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:42.881 default [http-nio-8884-exec-7] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:42.926 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:42,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:42.973 default [http-nio-8884-exec-4] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:43.027 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:43,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:43.375 default [http-nio-8884-exec-6] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:43.421 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:43,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:44.132 default [http-nio-8884-exec-5] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:44.180 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:44,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:44.619 default [http-nio-8884-exec-8] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:44.672 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:44,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:45.083 default [http-nio-8884-exec-10] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:45.235 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:45,-1,未知错误Failed to parse multipart servlet request; nested exception is java.io.IOException: org.apache.tomcat.util.http.fileupload.impl.IOFileUploadException: Processing of multipart/form-data request failed. java.io.EOFException
2021-12-13 12:55:45.375 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:45,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:55:45.888 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/黑客大曝光(第7版).pdf' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:55:45.941 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:55:45,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:43.337 default [http-nio-8884-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:43.366 default [http-nio-8884-exec-7] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:43.382 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:43,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:43.412 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:43,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:44.961 default [http-nio-8884-exec-6] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:44.988 default [http-nio-8884-exec-4] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:45.006 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:45,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:45.035 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:45,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:45.314 default [http-nio-8884-exec-5] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:45.372 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:45,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:46.850 default [http-nio-8884-exec-2] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:46.894 default [http-nio-8884-exec-8] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:46.896 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:46,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:46.953 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:46,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:47.268 default [http-nio-8884-exec-10] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:47.313 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:47,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:48.564 default [http-nio-8884-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:48.619 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:48,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:48.632 default [http-nio-8884-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:48.677 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:48,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 12:56:49.792 default [http-nio-8884-exec-3] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.20-windows-x64.zip' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 12:56:49.837 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 12:56:49,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 13:11:38.628 default [http-nio-8884-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/apache-tomcat-9.0.0.M6.tar.gz' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-13 13:11:38.678 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 13:11:38,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-13 14:34:40.235 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shutdownManager' defined in file [D:\cbw\project\disk-api-master\target\classes\com\novel\disk\manager\ShutdownManager.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.novel.disk.manager.ShutdownManager]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problem: 
	The declared package "com.jeethink.framework.manager" does not match the expected package "com.novel.disk.manager"

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1316)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.novel.disk.manager.ShutdownManager]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problem: 
	The declared package "com.jeethink.framework.manager" does not match the expected package "com.novel.disk.manager"

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:221)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1308)
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problem: 
	The declared package "com.jeethink.framework.manager" does not match the expected package "com.novel.disk.manager"

	at com.novel.disk.manager.ShutdownManager.<init>(ShutdownManager.java:1)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	... 24 common frames omitted
2021-12-13 16:27:17.388 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectingArgumentResolverBeanPostProcessor' defined in class path resource [org/springframework/data/web/config/ProjectingArgumentResolverRegistrar.class]: BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:270)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:762)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:567)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:111)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 19 common frames omitted
Caused by: java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.aspectj.weaver.tools.PointcutParser.parsePointcutExpression(PointcutParser.java:319)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.buildPointcutExpression(AspectJExpressionPointcut.java:227)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.obtainPointcutExpression(AspectJExpressionPointcut.java:198)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.getClassFilter(AspectJExpressionPointcut.java:177)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:226)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:289)
	at org.springframework.aop.support.AopUtils.findAdvisorsThatCanApply(AopUtils.java:321)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findAdvisorsThatCanApply(AbstractAdvisorAutoProxyCreator.java:128)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findEligibleAdvisors(AbstractAdvisorAutoProxyCreator.java:97)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.getAdvicesAndAdvisorsForBean(AbstractAdvisorAutoProxyCreator.java:78)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.wrapIfNecessary(AbstractAutoProxyCreator.java:339)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessAfterInitialization(AbstractAutoProxyCreator.java:291)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 41 common frames omitted
2021-12-13 16:31:47.638 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectingArgumentResolverBeanPostProcessor' defined in class path resource [org/springframework/data/web/config/ProjectingArgumentResolverRegistrar.class]: BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:270)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:762)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:567)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:111)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 19 common frames omitted
Caused by: java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.aspectj.weaver.tools.PointcutParser.parsePointcutExpression(PointcutParser.java:319)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.buildPointcutExpression(AspectJExpressionPointcut.java:227)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.obtainPointcutExpression(AspectJExpressionPointcut.java:198)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.getClassFilter(AspectJExpressionPointcut.java:177)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:226)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:289)
	at org.springframework.aop.support.AopUtils.findAdvisorsThatCanApply(AopUtils.java:321)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findAdvisorsThatCanApply(AbstractAdvisorAutoProxyCreator.java:128)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findEligibleAdvisors(AbstractAdvisorAutoProxyCreator.java:97)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.getAdvicesAndAdvisorsForBean(AbstractAdvisorAutoProxyCreator.java:78)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.wrapIfNecessary(AbstractAutoProxyCreator.java:339)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessAfterInitialization(AbstractAutoProxyCreator.java:291)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 41 common frames omitted
2021-12-13 16:32:07.611 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectingArgumentResolverBeanPostProcessor' defined in class path resource [org/springframework/data/web/config/ProjectingArgumentResolverRegistrar.class]: BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:270)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:762)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:567)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:111)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 19 common frames omitted
Caused by: java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.aspectj.weaver.tools.PointcutParser.parsePointcutExpression(PointcutParser.java:319)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.buildPointcutExpression(AspectJExpressionPointcut.java:227)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.obtainPointcutExpression(AspectJExpressionPointcut.java:198)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.getClassFilter(AspectJExpressionPointcut.java:177)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:226)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:289)
	at org.springframework.aop.support.AopUtils.findAdvisorsThatCanApply(AopUtils.java:321)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findAdvisorsThatCanApply(AbstractAdvisorAutoProxyCreator.java:128)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findEligibleAdvisors(AbstractAdvisorAutoProxyCreator.java:97)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.getAdvicesAndAdvisorsForBean(AbstractAdvisorAutoProxyCreator.java:78)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.wrapIfNecessary(AbstractAutoProxyCreator.java:339)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessAfterInitialization(AbstractAutoProxyCreator.java:291)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 41 common frames omitted
2021-12-13 16:42:00.883 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectingArgumentResolverBeanPostProcessor' defined in class path resource [org/springframework/data/web/config/ProjectingArgumentResolverRegistrar.class]: BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:270)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:762)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:567)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': Initialization of bean failed; nested exception is java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:111)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 19 common frames omitted
Caused by: java.lang.IllegalArgumentException: error Type referred to is not an annotation type: com$novel$common$annotation$Log
	at org.aspectj.weaver.tools.PointcutParser.parsePointcutExpression(PointcutParser.java:319)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.buildPointcutExpression(AspectJExpressionPointcut.java:227)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.obtainPointcutExpression(AspectJExpressionPointcut.java:198)
	at org.springframework.aop.aspectj.AspectJExpressionPointcut.getClassFilter(AspectJExpressionPointcut.java:177)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:226)
	at org.springframework.aop.support.AopUtils.canApply(AopUtils.java:289)
	at org.springframework.aop.support.AopUtils.findAdvisorsThatCanApply(AopUtils.java:321)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findAdvisorsThatCanApply(AbstractAdvisorAutoProxyCreator.java:128)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findEligibleAdvisors(AbstractAdvisorAutoProxyCreator.java:97)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.getAdvicesAndAdvisorsForBean(AbstractAdvisorAutoProxyCreator.java:78)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.wrapIfNecessary(AbstractAutoProxyCreator.java:339)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessAfterInitialization(AbstractAutoProxyCreator.java:291)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 41 common frames omitted
2021-12-13 16:44:14.060 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logRepository' defined in com.novel.disk.repository.LogRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.common.obj.SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:925)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.common.obj.SysOperLog
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:582)
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:85)
	at org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation.<init>(JpaMetamodelEntityInformation.java:75)
	at org.springframework.data.jpa.repository.support.JpaEntityInformationSupport.getEntityInformation(JpaEntityInformationSupport.java:66)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getEntityInformation(JpaRepositoryFactory.java:228)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:178)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:161)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:72)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:319)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:230)
	at org.springframework.data.util.Lazy.get(Lazy.java:114)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1845)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 21 common frames omitted
2021-12-13 16:48:20.106 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectingArgumentResolverBeanPostProcessor' defined in class path resource [org/springframework/data/web/config/ProjectingArgumentResolverRegistrar.class]: BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': BeanPostProcessor before instantiation of bean failed; nested exception is java.lang.NoClassDefFoundError: SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:270)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:762)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:567)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration': BeanPostProcessor before instantiation of bean failed; nested exception is java.lang.NoClassDefFoundError: SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:519)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:111)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 19 common frames omitted
Caused by: java.lang.NoClassDefFoundError: SysOperLog
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.aspectj.internal.lang.reflect.AjTypeImpl.getDeclarePrecedence(AjTypeImpl.java:1050)
	at org.springframework.aop.aspectj.annotation.AspectMetadata.<init>(AspectMetadata.java:97)
	at org.springframework.aop.aspectj.annotation.BeanFactoryAspectJAdvisorsBuilder.buildAspectJAdvisors(BeanFactoryAspectJAdvisorsBuilder.java:106)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:95)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1142)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1117)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:513)
	... 40 common frames omitted
Caused by: java.lang.ClassNotFoundException: SysOperLog
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:348)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:145)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	... 52 common frames omitted
2021-12-13 16:50:35.949 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logRepository' defined in com.novel.disk.repository.LogRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:925)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:582)
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:85)
	at org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation.<init>(JpaMetamodelEntityInformation.java:75)
	at org.springframework.data.jpa.repository.support.JpaEntityInformationSupport.getEntityInformation(JpaEntityInformationSupport.java:66)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getEntityInformation(JpaRepositoryFactory.java:228)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:178)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:161)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:72)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:319)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:230)
	at org.springframework.data.util.Lazy.get(Lazy.java:114)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1845)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 21 common frames omitted
2021-12-13 16:51:18.351 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logRepository' defined in com.novel.disk.repository.LogRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:925)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:582)
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:85)
	at org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation.<init>(JpaMetamodelEntityInformation.java:75)
	at org.springframework.data.jpa.repository.support.JpaEntityInformationSupport.getEntityInformation(JpaEntityInformationSupport.java:66)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getEntityInformation(JpaRepositoryFactory.java:228)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:178)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:161)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:72)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:319)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:230)
	at org.springframework.data.util.Lazy.get(Lazy.java:114)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1845)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 21 common frames omitted
2021-12-13 16:53:05.284 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logRepository' defined in com.novel.disk.repository.LogRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:925)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:582)
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:85)
	at org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation.<init>(JpaMetamodelEntityInformation.java:75)
	at org.springframework.data.jpa.repository.support.JpaEntityInformationSupport.getEntityInformation(JpaEntityInformationSupport.java:66)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getEntityInformation(JpaRepositoryFactory.java:228)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:178)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:161)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:72)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:319)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:230)
	at org.springframework.data.util.Lazy.get(Lazy.java:114)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1845)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 21 common frames omitted
2021-12-13 16:55:35.558 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logRepository' defined in com.novel.disk.repository.LogRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:925)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalArgumentException: Not a managed type: class com.novel.disk.entity.SysOperLog
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:582)
	at org.hibernate.metamodel.internal.MetamodelImpl.managedType(MetamodelImpl.java:85)
	at org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation.<init>(JpaMetamodelEntityInformation.java:75)
	at org.springframework.data.jpa.repository.support.JpaEntityInformationSupport.getEntityInformation(JpaEntityInformationSupport.java:66)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getEntityInformation(JpaRepositoryFactory.java:228)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:178)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:161)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactory.getTargetRepository(JpaRepositoryFactory.java:72)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:319)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:230)
	at org.springframework.data.util.Lazy.get(Lazy.java:114)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1845)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 21 common frames omitted
2021-12-13 16:58:25.744 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-13 16:58:25,-1,未知错误Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
