package com.novel.disk.common.utils.oss;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FilenameUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.novel.disk.common.DateUtils;
import com.novel.disk.common.exception.BaseException;
import com.novel.disk.common.obj.PathVariableObj;
import com.novel.disk.common.uuid.IdUtils;
import com.novel.disk.entity.CommonProperties;
import com.novel.disk.framework.config.ChjdConfig;
import com.novel.disk.framework.exception.BusinessException;

import lombok.SneakyThrows;


/**
 *  OSS工具类
 * <AUTHOR>
 *
 */
public class OssUtils {
	
	private static String endPoint = CommonProperties.getProperty("aliyun.oss.endPoint");
	
	private static String accessKeyId = CommonProperties.getProperty("aliyun.oss.keyId");
    
	private static String accessKeySecret = CommonProperties.getProperty("aliyun.oss.keySecret");
    
	private static String bucketName = CommonProperties.getProperty("aliyun.oss.bucketName");

	/**
	 *  获取阿里云OSS客户端连接
	 * @return
	 */
	public static OSS getOssClient() {
        OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);
        return ossClient;
	}
	
	/**
	 *  上传文件到OSS
	 * @param file 要上传的文件
	 * @param pathObj 上传文件的路径组成信息
	 * @return savePath 文件保存的地址
	 */
	public static String uploadToOss(MultipartFile file, PathVariableObj pathObj) throws IOException {
		
		// 文件预处理
		String fileName = extractFilename(file, pathObj);
		String baseDir = ChjdConfig.getName();
		String uploadPath = baseDir + "/" + fileName;
		
        // 连接oss客户端
        OSS ossClient = getOssClient();
        
        // 上传文件流
        InputStream inputStream = file.getInputStream();
        
        // 上传
        ossClient.putObject(bucketName, uploadPath, inputStream);
        // 关闭连接
        ossClient.shutdown();
        
        return uploadPath;
	}
	
	/**
	 *  上传普通文件到OSS
	 */
	public static String uploadToOss(File file, String uploadPath) throws IOException {
		
        // 连接oss客户端
        OSS ossClient = getOssClient();
        
        // 上传文件流
        InputStream inputStream = new FileInputStream(file);
        
        // 上传
        ossClient.putObject(bucketName, uploadPath, inputStream);
        
        // 关闭连接
        ossClient.shutdown();
        
        return uploadPath;
	}
	
	/**
	 *  文件流方式上传文件到OSS
	 */
	public static String uploadToOss(InputStream inputStream, String uploadPath) throws IOException {
		
		// 连接oss客户端
		OSS ossClient = getOssClient();
		
		// 检查文件是否存在，存在即删除原来的文件
		if (ossClient.doesObjectExist(bucketName, uploadPath)) {
			deleteOssFile(uploadPath);
		}
		// 上传
		ossClient.putObject(bucketName, uploadPath, inputStream);
		
		// 关闭连接
		ossClient.shutdown();
		
		return uploadPath;
	}
	
	/**
	 *  从OSS上下载文件
	 */
    public static void downloadFromOss(final HttpServletResponse response, String filePath, String fileName) throws IOException {
    	
        // 连接oss客户端
        OSS ossClient = getOssClient();
        
    	// 根据路径校验文件是否存在
    	if (ossClient.doesObjectExist(bucketName, filePath)) {
    		
    		response.setHeader("content-type", "application/octet-stream");
    		response.setContentType("application/octet-stream");
    		
    		// 下载文件能正常显示中文
    		try {
    			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
    		} catch (UnsupportedEncodingException e) {
    			e.printStackTrace();
    		}
    		
    		// 实现文件下载
    		
    		OSSObject object = ossClient.getObject(bucketName,filePath);
			response.setContentLength(Integer.parseInt(object.getObjectMetadata().getContentLength() + ""));
    		
    		byte[] buffer = new byte[1024 * 10];
    		InputStream ins = object.getObjectContent();
    		BufferedInputStream bis = null;
    		try {
    			bis = new BufferedInputStream(ins);
    			OutputStream os = response.getOutputStream();
    			int i = bis.read(buffer);
    			while (i != -1) {
    				os.write(buffer, 0, i);
    				i = bis.read(buffer);
    			}
    			os.close();
    		} catch (Exception e) {
    			e.printStackTrace();
    		} finally {
    			assert bis != null;
    			bis.close();
    			ins.close();
    			ossClient.shutdown();
    		}
    	}
    }
    
    /**
	 *  从OSS上下载文件
	 * @return 字节数组
	 */
    @SneakyThrows
    public static byte[] downloadFromOss(String filePath) {
    	
		Long startTime = System.currentTimeMillis();
        // 连接oss客户端
        OSS ossClient = getOssClient();
        
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        
    	// 根据路径校验文件是否存在
    	if (ossClient.doesObjectExist(bucketName, filePath)) {
    		
    		OSSObject object = ossClient.getObject(bucketName,filePath);
			// GetObjectProgressListener progressListener = new GetObjectProgressListener();
			// OSSObject object = ossClient.getObject(new GetObjectRequest(bucketName, filePath).<GetObjectRequest>withProgressListener(progressListener));
    		InputStream ins = object.getObjectContent();
    		byte[] buffer = new byte[1024];
    		int i = -1;
    		try {
    			while ((i = ins.read(buffer)) != -1) {
    				bos.write(buffer, 0, i);
    			}
    			bos.close();
    		} catch (Exception e) {
    			e.printStackTrace();
    		} finally {
    			assert ins != null;
    			ins.close();
    			ossClient.shutdown();
    		}
    	}
		Long endTime = System.currentTimeMillis();
		System.out.println("totalTime:" + (endTime - startTime));
    	return bos.toByteArray();
    }
    
    /**
     *  从OSS上下载文件保存到本地
     */
    
    /**
     *  从OSS上打包下载文件
     */
    public static void downloadZipFromOss(List<String> filePaths,HttpServletResponse response) throws Exception{
		response.setCharacterEncoding("utf-8");
		response.setHeader("content-type", "application/octet-stream");
		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename=0001.zip");
		BufferedInputStream bis = null;
		try {
			ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
			OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);
			for (String filePath : filePaths) {
				if (filePath == null || filePath.equals("")) {
					continue;
				}
				Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
				GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, filePath, HttpMethod.GET);
				// 设置过期时间。
				request.setExpiration(expiration);
				// 生成签名URL（HTTP GET请求）。
				URL signedUrl = ossClient.generatePresignedUrl(request);
				// 使用签名URL发送请求。
				OSSObject ossObject = ossClient.getObject(signedUrl, new HashMap<>());

				if (ossObject != null) {
					InputStream inputStream = ossObject.getObjectContent();
					byte[] buffs = new byte[1024 * 10];

					String zipFile = filePath.substring(filePath.lastIndexOf("/") + 1);
					ZipEntry zipEntry = new ZipEntry(zipFile);
					zos.putNextEntry(zipEntry);
					bis = new BufferedInputStream(inputStream, 1024 * 10);

					int read;
					while ((read = bis.read(buffs, 0, 1024 * 10)) != -1) {
						zos.write(buffs, 0, read);
					}
					ossObject.close();
				}
			}
			zos.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//关闭流
			try {
				if (null != bis) {
					bis.close();
				}
				response.getOutputStream().flush();
				response.getOutputStream().close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

	}
	/**
	 * 从OSS上打包下载文件【返回文件流】
	 * @param response
	 * @param fileAttrs
	 * @param zipFileName
	 * @return
	 * @throws Exception
	 */
	public static void downloadZipFromOss(HttpServletResponse resp, List<HashMap<String, Object>> fileAttrs, String zipFileName) throws Exception{
		resp.setCharacterEncoding("utf-8");
		resp.setHeader("content-type", "application/octet-stream");
		resp.setHeader("Content-Disposition", "attachment;filename="+zipFileName);
		BufferedInputStream bis = null;
		try {
			ZipOutputStream zos = new ZipOutputStream(resp.getOutputStream());
			OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);
			for (HashMap<String, Object> fileAttr : fileAttrs) {
				String filePath = (String) fileAttr.get("filePath");
				String fileName = (String) fileAttr.get("fileName");
				if (filePath == null || filePath.equals("")) {
					continue;
				}
				Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
				GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, filePath, HttpMethod.GET);
				// 设置过期时间。
				request.setExpiration(expiration);
				// 生成签名URL（HTTP GET请求）。
				URL signedUrl = ossClient.generatePresignedUrl(request);
				// 使用签名URL发送请求。
				OSSObject ossObject = ossClient.getObject(signedUrl, new HashMap<>());

				if (ossObject != null) {
					InputStream inputStream = ossObject.getObjectContent();
					byte[] buffs = new byte[1024 * 10];

					// fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
					ZipEntry zipEntry = new ZipEntry(fileName);
					zos.putNextEntry(zipEntry);
					bis = new BufferedInputStream(inputStream, 1024 * 10);

					int read;
					while ((read = bis.read(buffs, 0, 1024 * 10)) != -1) {
						zos.write(buffs, 0, read);
					}
					ossObject.close();
				}
			}
			zos.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//关闭流
			try {
				if (null != bis) {
					bis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
    
    /**
     *  从OSS上打包下载文件【返回字节流】
     * @return byte[]
     */
    public static byte[] downloadZipFromOss(List<String> filePaths) throws Exception{
		BufferedInputStream bis = null;
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try {
			ZipOutputStream zos = new ZipOutputStream(outputStream);
			OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);
			for (String filePath : filePaths) {
				if (filePath == null || filePath.equals("")) {
					continue;
				}
				Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
				GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, filePath, HttpMethod.GET);
				// 设置过期时间。
				request.setExpiration(expiration);
				// 生成签名URL（HTTP GET请求）。
				URL signedUrl = ossClient.generatePresignedUrl(request);
				// 使用签名URL发送请求。
				OSSObject ossObject = ossClient.getObject(signedUrl, new HashMap<>());

				if (ossObject != null) {
					InputStream inputStream = ossObject.getObjectContent();
					byte[] buffs = new byte[1024 * 10];

					String zipFile = filePath.substring(filePath.lastIndexOf("/") + 1);
					ZipEntry zipEntry = new ZipEntry(zipFile);
					zos.putNextEntry(zipEntry);
					bis = new BufferedInputStream(inputStream, 1024 * 10);

					int read;
					while ((read = bis.read(buffs, 0, 1024 * 10)) != -1) {
						zos.write(buffs, 0, read);
					}
					ossObject.close();
				}
			}
			zos.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//关闭流
			try {
				if (null != bis) {
					bis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return outputStream.toByteArray();
	}
    
	/**
	 *  编码文件名
	 */
	public static final String extractFilename(MultipartFile file, PathVariableObj pathObj) {
//		String fileName = file.getOriginalFilename();
		// 设备编码+文件业务类型（中文）+unix时间戳+文件后缀
		String fileName = pathObj.getThirdLevel() + "-" + pathObj.getFourthLevel() + "-" + IdUtils.fastSimpleUUID().substring(25)
		+ "." + FilenameUtils.getExtension(file.getOriginalFilename());
		if (pathObj != null) {
			String prefix = "";
			if (pathObj.getFourthLevel() != null) {
				prefix += pathObj.getFirstLevel() + "/" + pathObj.getSecondLevel() + "/"
						+ pathObj.getThirdLevel() + "/" + pathObj.getFourthLevel();
			} else {
				prefix += pathObj.getFirstLevel() + "/" + pathObj.getSecondLevel() + "/"
						+ pathObj.getThirdLevel();
			}
			fileName = prefix + "/" + fileName;
		} else {
			fileName = DateUtils.datePath() + "/" + IdUtils.fastUUID();
		}
		return fileName;
	}
	
	/**
	 *  删除OSS上的文件
	 */
	public static void deleteOssFile(String savePath) {
		OSS oss = getOssClient();
		if (oss.doesObjectExist(bucketName, savePath)) {
			oss.deleteObject(bucketName, savePath);
		}
		oss.shutdown();
	}
	
	/**
	 *  删除OSS上的目录及目录下的所有文件 
	 */
	public static void deleteOssDir(String savePath) {

		// 填写不包含Bucket名称在内的目录完整路径。例如Bucket下testdir目录的完整路径为testdir/。
//		final String prefix = "testdir/";
		OSS oss = getOssClient();

		// 删除目录及目录下的所有文件。
		String nextMarker = null;
		ObjectListing objectListing = null;
		do {
		    ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName)
		            .withPrefix(savePath)
		            .withMarker(nextMarker);

		    objectListing = oss.listObjects(listObjectsRequest);
		    if (objectListing.getObjectSummaries().size() > 0) {
		        List<String> keys = new ArrayList<String>();
		        for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
		            System.out.println("key name: " + s.getKey());
		            keys.add(s.getKey());
		        }
		        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName).withKeys(keys);
		        DeleteObjectsResult deleteObjectsResult = oss.deleteObjects(deleteObjectsRequest);
		        List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
		        try {
		            for(String obj : deletedObjects) {
		                String deleteObj =  URLDecoder.decode(obj, "UTF-8");
		                System.out.println(deleteObj);
		            }
		        } catch (UnsupportedEncodingException e) {
		            e.printStackTrace();
		        }
		    }

		    nextMarker = objectListing.getNextMarker();
		} while (objectListing.isTruncated());

		// 关闭OSSClient。
		oss.shutdown();
	}
	
	
	/**
	 *  根据前缀获取bucket的文件列表
	 */
	public static List<String> getOssFileList(String keyPrefix) {
		
		List<String> fileNameList = new ArrayList<String>();
		
		// 创建OSSClient实例。
		OSS ossClient = getOssClient();

		// 列举文件。如果不设置KeyPrefix，则列举存储空间下的所有文件。如果设置KeyPrefix，则列举包含指定前缀的文件。
		ObjectListing objectListing = ossClient.listObjects(bucketName, keyPrefix);
		List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
		for (OSSObjectSummary s : sums) {
			fileNameList.add(s.getKey());
		}

		// 关闭OSSClient。
		ossClient.shutdown();
		
		return fileNameList;
	}
	
	/**
	 *  生成签名url
	 */
	public static String generateSignedUrl(String savePath) {
		OSS oss = getOssClient();

		// 设置签名URL过期时间为3600秒（1小时）。
		Date expiration = new Date(new Date().getTime() + 3600 * 1000);
		// 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
		URL url = oss.generatePresignedUrl(bucketName, savePath, expiration);
		// 关闭OSSClient。
		oss.shutdown(); 
		return url.toString();
	}
	
	/**
	 *  从oss上下载资源并存储到本地
	 */
	public static boolean downloadAndStoreToLocal(String filePath, String localPath) {

		// 创建OSSClient实例。
		OSS ossClient = getOssClient();
		try {
			// 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
			// 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
			String fileName = filePath.substring(filePath.lastIndexOf("/")+1,filePath.length());
			System.out.println("开始下载"+fileName+"到本地"+localPath);
			ossClient.getObject(new GetObjectRequest(bucketName, filePath), new File(localPath+fileName));
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} finally {
			// 关闭OSSClient。
			ossClient.shutdown();
		}

		return true;
	}
	
	/**
	 *  在OSS上创建目录
	 * @throws IOException 
	 */
	public static boolean mkOssDir(String ossPath) throws IOException {
        // 连接oss客户端
        OSS ossClient = getOssClient();
        
        // 创建临时文件
        File file = new File("tmp.txt");
        file.createNewFile();
        // 上传
        System.out.println(ossPath);
        ossClient.putObject(bucketName, ossPath + "/tmp.txt", file);
        file.delete();
        
        // 关闭连接
        ossClient.shutdown();
        
        return true;
	}
	
	/** 
	 *  重命名/移动OSS上的文件【删除再上传】【先copy，再删除】
	 * @throws IOException 
	 */
	public static boolean moveOssFile(String oldPath, String newPath) {
		System.out.println("oldPath: "+oldPath);
		System.out.println("newPath: "+newPath);
		OSS oss = getOssClient();
		try {
			OSSObject obj = oss.getObject(bucketName, oldPath);
			deleteOssFile(oldPath);
			uploadToOss(obj.getObjectContent(), newPath);
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} finally {
			oss.shutdown();
		}
		return true;
	}
	
	/**
	 *  复制OSS上的文件到新的位置
	 */
	public static boolean copyOssFile(String source, String target) {
		OSS oss = getOssClient();
		oss.copyObject(bucketName, source, bucketName, target);
		oss.shutdown();
		return true;
	}
	
	/**
	 *  根据prefix列举文件
	 */
	public static List<String> listOssFile(String prefix) {
		List<String> fileNameList = new ArrayList<String>();
		OSS oss = getOssClient();
		ObjectListing objectListing = oss.listObjects(bucketName, prefix);
		List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
		for (OSSObjectSummary s : sums) {
			fileNameList.add(s.getKey());
		}
		oss.shutdown();
		return fileNameList;
	}

	/**
	 * 获取文件长度
	 */
	public static long getOssFileLength(String filePath) {
		OSS oss = getOssClient();
		OSSObject obj = oss.getObject(bucketName, filePath);
		long length = obj.getObjectMetadata().getContentLength();
		oss.shutdown();
		return length;
	}
	
}
