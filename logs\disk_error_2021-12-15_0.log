2021-12-15 10:21:58.990 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-15 10:21:58,-1,未知错误Required request parameter 'fileId' for method parameter type Long is not present
2021-12-15 10:23:17.652 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-15 10:23:17,-1,未知错误Required request parameter 'fileId' for method parameter type Long is not present
2021-12-15 10:24:11.325 default [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'fileController' defined in file [D:\cbw\project\chjd-disk\target\classes\com\novel\disk\controller\FileController.class]: Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.novel.disk.controller.FileController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problem: 
	Syntax error on token "GetMapping", ( expected after this token

	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:296)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1354)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1332)
	at com.novel.disk.DiskApplication.main(DiskApplication.java:29)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.novel.disk.controller.FileController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problem: 
	Syntax error on token "GetMapping", ( expected after this token

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:221)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:311)
	... 24 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problem: 
	Syntax error on token "GetMapping", ( expected after this token

	at com.novel.disk.controller.FileController.<init>(FileController.java:176)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	... 26 common frames omitted
2021-12-15 11:07:34.634 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-15 11:07:34,-1,未知错误Required request parameter 'sourceListId' for method parameter type Long[] is not present
2021-12-15 11:22:17.204 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-15 11:22:17,-1,未知错误For input string: "[253]"
