package com.novel.disk.controller;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;

import com.novel.disk.common.BaseController;
import com.novel.disk.common.Result;
import com.novel.disk.framework.config.LoginProperties;
import com.novel.disk.service.FileAuthService;

/**
 * 用户 控制器
 *
 * <AUTHOR>
 * @date 2020/7/24
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    private final RestTemplate restTemplate;
    private final LoginProperties loginProperties;
    private final FileAuthService fileAuthService;

    public UserController(RestTemplate restTemplate, LoginProperties loginProperties, FileAuthService fileAuthService) {
        this.restTemplate = restTemplate;
        this.loginProperties = loginProperties;
        this.fileAuthService = fileAuthService;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public Result getUserInfo() {
        HashMap<String, Object> map = new HashMap<>(16);
        map.put("user", getUser());
        // 获取用户权限
        map.put("perms", fileAuthService.getFileAuthByUserId(getUserId()));
        map.put("isRoot", fileAuthService.isRootUser(getUserId()));
        return Result.success(map);
    }

    /**
     * 用户登出
     *
     * @return 用户信息
     */
    @GetMapping("/logout")
    public Result logout(@RequestHeader("Authorization") String token) {
        String url = loginProperties.getUrl() + "/logout";
        HttpHeaders header = new HttpHeaders();
        header.set("Authorization", token);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(header);
        ResponseEntity<Result> entity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Result.class);
        return toAjax(entity.getBody());
    }

    /**
     * 获取用户树【实际上是部门用户树，在部门列表中加入子节点，子节点中全是所属用户即可】
     */
    @GetMapping("/getUserTree")
    public Result getUserTree(@RequestHeader("Authorization") String token) {
        String url = loginProperties.getUrl() + "/system/user/getUserTree";
        HttpHeaders header = new HttpHeaders();
        header.set("Authorization", token);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(header);
        ResponseEntity<Result> entity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, Result.class);
        return Result.success(entity.getBody().getData());
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/getUserList")
    public Result getUserList(@RequestHeader("Authorization") String token) {
        String url = loginProperties.getUrl() + "/system/user/list";
        HttpHeaders header = new HttpHeaders();
        header.set("Authorization", token);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(header);
        ResponseEntity<Result> entity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, Result.class);
        return Result.success(entity.getBody().getData());
    }
}
