package com.novel.disk.common.utils.oss;

import com.aliyun.oss.event.ProgressEvent;
import com.aliyun.oss.event.ProgressEventType;
import com.aliyun.oss.event.ProgressListener;

public class GetObjectProgressListener implements ProgressListener {
    private long bytesRead = 0;
    private long totalBytes = -1;
    private boolean succeed = false;
    @Override
    public void progressChanged(ProgressEvent progressEvent) {
        long bytes = progressEvent.getBytes();
        ProgressEventType eventType = progressEvent.getEventType();
        switch (eventType) {
        case TRANSFER_STARTED_EVENT:
            System.out.println("Start to download......");
            break;
        case RESPONSE_CONTENT_LENGTH_EVENT:
            this.totalBytes = bytes;
            System.out.println(this.totalBytes + " bytes in total will be downloaded to a local file");
            break;
        case RESPONSE_BYTE_TRANSFER_EVENT:
            this.bytesRead += bytes;
            if (this.totalBytes != -1) {
                int percent = (int)(this.bytesRead * 100.0 / this.totalBytes);
                System.out.println(bytes + " bytes have been read at this time, download progress: " +
                        percent + "%(" + this.bytesRead + "/" + this.totalBytes + ")");
            } else {
                System.out.println(bytes + " bytes have been read at this time, download ratio: unknown" +
                        "(" + this.bytesRead + "/...)");
            }
            break;
        case TRANSFER_COMPLETED_EVENT:
            this.succeed = true;
            System.out.println("Succeed to download, " + this.bytesRead + " bytes have been transferred in total");
            break;
        case TRANSFER_FAILED_EVENT:
            System.out.println("Failed to download, " + this.bytesRead + " bytes have been transferred");
            break;
        default:
            break;
        }
    }
    public boolean isSucceed() {
        return succeed;
    }

    public long getTotalBytes() {
        return totalBytes;
    }
}