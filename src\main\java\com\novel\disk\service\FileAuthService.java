package com.novel.disk.service;

import java.util.HashMap;
import java.util.List;

import com.novel.disk.entity.FileAuthEntity;

import org.springframework.data.domain.Page;

public interface FileAuthService {

    /**
     * 根据id查询用户所拥有的权限
     * @param userId
     */
    List<FileAuthEntity> getFileAuthByUserId(Long userId);

    /**
     * 根据id查询权限
     * @param fileAuthId
     */
    FileAuthEntity getByFileAuthId(Long fileAuthId);

    /**
     * 判断用户是否是root用户
     */
    boolean isRootUser(Long userId);

     /**
     * 根据文件ID查询文件权限
     * 
     * @param fileId 文件ID
     * @return 文件权限
     */
    public List<FileAuthEntity> selectFileAuthByFileId(Long fileId);

    /**
     * 查询文件权限列表
     * 
     * @param FileAuthEntity 文件权限
     * @return 文件权限集合
     */
    public List<FileAuthEntity> selectFileAuthList(FileAuthEntity fileAuthEntity);

    /**
     * 查询文件权限总条数
     */
    public long selectFileAuthCount(FileAuthEntity FileAuthEntity);

    /**
     * 新增文件权限
     * 
     * @param FileAuthEntity 文件权限
     * @return 结果
     */
    public int insertFileAuth(FileAuthEntity FileAuthEntity);

    /**
     * 批量新增文件权限
     * 
     * @param FileAuthEntity 文件权限
     * @return 结果
     */
    public int batchAddFileAuth(List<FileAuthEntity> FileAuthEntityList);

    /**
     * 修改文件权限
     * 
     * @param FileAuthEntity 文件权限
     * @return 结果
     */
    public int updateFileAuth(FileAuthEntity FileAuthEntity);

    /**
     * 批量删除文件权限
     * 
     * @param fileAuthIds 需要删除的文件权限ID
     * @return 结果
     */
    public int deleteFileAuthByIds(Long[] fileAuthIds);

    /**
     * 删除文件权限信息
     * 
     * @param fileAuthId 文件权限ID
     * @return 结果
     */
    public int deleteFileAuthById(Long fileAuthId);

}