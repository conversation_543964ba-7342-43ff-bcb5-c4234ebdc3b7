2021-12-10 10:29:41.844 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 10:29:41,-1,未知错误No value present
2021-12-10 10:29:42.354 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 10:29:42,-1,未知错误No value present
2021-12-10 13:26:02.699 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 参数异常，2021-12-10 13:26:02,200,org.springframework.validation.BeanPropertyBindingResult: 1 errors
Field error in object 'fileEntity' on field 'type': rejected value [NaN]; codes [typeMismatch.fileEntity.type,typeMismatch.type,typeMismatch.java.lang.Integer,typeMismatch]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [fileEntity.type,type]; arguments []; default message [type]]; default message [Failed to convert property value of type 'java.lang.String' to required type 'java.lang.Integer' for property 'type'; nested exception is java.lang.NumberFormatException: For input string: "NaN"]
2021-12-10 15:11:20.647 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:11:20,-1,未知错误Required request part 'files' is not present
2021-12-10 15:11:20.862 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:11:20,-1,未知错误Required request part 'files' is not present
2021-12-10 15:11:20.929 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:11:20,-1,未知错误Required request part 'files' is not present
2021-12-10 15:13:26.501 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:13:26,-1,未知错误Required request part 'files' is not present
2021-12-10 15:13:26.546 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:13:26,-1,未知错误Required request part 'files' is not present
2021-12-10 15:13:26.760 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:13:26,-1,未知错误Required request part 'files' is not present
2021-12-10 15:14:58.808 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:14:58,-1,未知错误Required request part 'files' is not present
2021-12-10 15:14:58.831 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:14:58,-1,未知错误Required request part 'files' is not present
2021-12-10 15:14:59.160 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 15:14:59,-1,未知错误Required request part 'files' is not present
2021-12-10 16:15:57.088 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:15:57,-1,未知错误文件名重复
2021-12-10 16:15:57.096 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:15:57,-1,未知错误文件名重复
2021-12-10 16:15:57.088 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:15:57,-1,未知错误文件名重复
2021-12-10 16:20:51.073 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:20:51,-1,未知错误文件名重复
2021-12-10 16:20:51.378 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:20:51,-1,未知错误文件名重复
2021-12-10 16:20:51.390 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:20:51,-1,未知错误文件名重复
2021-12-10 16:27:52.195 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:27:52,-1,未知错误文件名重复
2021-12-10 16:29:41.741 default [http-nio-8884-exec-5] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:41,-1,未知错误文件名重复
2021-12-10 16:29:41.758 default [http-nio-8884-exec-9] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:41,-1,未知错误文件名重复
2021-12-10 16:29:41.773 default [http-nio-8884-exec-10] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:41,-1,未知错误文件名重复
2021-12-10 16:29:42.000 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:42,-1,未知错误文件名重复
2021-12-10 16:29:42.299 default [http-nio-8884-exec-2] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:42,-1,未知错误文件名重复
2021-12-10 16:29:42.306 default [http-nio-8884-exec-1] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:42,-1,未知错误文件名重复
2021-12-10 16:29:42.359 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:42,-1,未知错误文件名重复
2021-12-10 16:29:42.537 default [http-nio-8884-exec-6] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:42,-1,未知错误文件名重复
2021-12-10 16:29:43.010 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:29:43,-1,未知错误文件名重复
2021-12-10 16:33:30.688 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:33:30,-1,未知错误文件名重复
2021-12-10 16:35:46.081 default [http-nio-8884-exec-7] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:35:46,-1,未知错误文件名重复
2021-12-10 16:41:31.981 default [http-nio-8884-exec-3] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:41:31,-1,未知错误文件名重复
2021-12-10 16:56:37.685 default [http-nio-8884-exec-4] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/test/1/401.gif' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-10 16:56:37.792 default [http-nio-8884-exec-4] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:56:37,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2021-12-10 16:56:52.899 default [http-nio-8884-exec-8] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry '/test/1/401.gif' for key 'file.UKo80ebeoofd7n89bc9iuwf6m0f'
2021-12-10 16:56:52.950 default [http-nio-8884-exec-8] ERROR c.n.d.f.e.h.GlobalExceptionHandler - 2021-12-10 16:56:52,-1,未知错误could not execute statement; SQL [n/a]; constraint [file.UKo80ebeoofd7n89bc9iuwf6m0f]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
